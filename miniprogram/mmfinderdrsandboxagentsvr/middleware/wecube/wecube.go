// Package wecube 全景监控 trpc log 插件
package wecube

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/plugin"
	"git.woa.com/wego/wxg/weenv"
	"git.woa.com/wego/wxg/wlog/cube"
	"go.uber.org/zap"
	"go.uber.org/zap/buffer"
	"go.uber.org/zap/zapcore"
)

const (
	pluginName = "wecube"
	pluginType = "log"
)

func init() {
	log.RegisterCoreLevelNewer(pluginName, &writerFactory{name: pluginName, factory: &Factory{}})
}

func newParamsCore(core zapcore.Core) *paramsCore {
	return &paramsCore{
		Core:   core,
		fields: make([]zapcore.Field, 0),
	}
}

type paramsCore struct {
	zapcore.Core
	fields []zapcore.Field
}

var fieldsEnc = func() zapcore.Encoder {
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.TimeKey = ""
	encoderConfig.LevelKey = ""
	encoderConfig.MessageKey = ""
	return zapcore.NewJSONEncoder(encoderConfig)
}()

func fieldsToJSON(fields []zapcore.Field) ([]byte, error) {
	var buffer bytes.Buffer
	core := zapcore.NewCore(fieldsEnc, zapcore.AddSync(&buffer), zapcore.DebugLevel)
	logger := zap.New(core)
	logger.Info("", fields...)
	_ = logger.Sync()

	data := buffer.Bytes()
	data = bytes.TrimSuffix(data, []byte("\n"))
	return data, nil
}

// With adds structured context to the Core.
func (c *paramsCore) With(fields []zapcore.Field) zapcore.Core {
	newCore := &paramsCore{
		Core:   c.Core,
		fields: append(c.fields, fields...),
	}
	return newCore
}

// Check determines whether the supplied Entry should be logged (using the
// embedded LevelEnabler and possibly some extra logic). If the entry
// should be logged, the Core adds itself to the CheckedEntry and returns
// the result.
func (c *paramsCore) Check(entry zapcore.Entry, ce *zapcore.CheckedEntry) *zapcore.CheckedEntry {
	if c.Enabled(entry.Level) {
		return ce.AddCore(entry, c)
	}
	return ce
}

// Write serializes the Entry and any Fields supplied at the log site and
// writes them to their destination.
func (c *paramsCore) Write(entry zapcore.Entry, fields []zapcore.Field) error {
	fieldsCount := len(c.fields) + len(fields)
	if fieldsCount == 0 {
		return c.Core.Write(entry, nil)
	}

	finalFields := make([]zapcore.Field, 0, 1)
	params := make([]zapcore.Field, 0, fieldsCount)
	for _, list := range [][]zapcore.Field{c.fields, fields} {
		for _, f := range list {
			if _, ok := fixedColumnName[f.Key]; !ok {
				params = append(params, f)
				continue
			}
			finalFields = append(finalFields, f)
		}
	}

	if len(params) > 0 {
		jsonData, _ := fieldsToJSON(params)
		finalFields = append(finalFields, zap.String(paramsColumnName, string(jsonData)))
	}
	return c.Core.Write(entry, finalFields)
}

type writerFactory struct {
	name    string
	factory plugin.Factory
}

func (w *writerFactory) New(config log.OutputConfig) (zapcore.Core, error) {
	decoder := &log.Decoder{OutputConfig: &config, Core: zapcore.NewNopCore()}

	if err := w.factory.Setup(w.name, decoder); err != nil {
		return nil, fmt.Errorf("setting up %s failed: %v", w.name, err)
	}
	return newParamsCore(decoder.Core), nil
}

// NewCoreLevel implements CoreLevelNewer interface.
func (w *writerFactory) NewCoreLevel(config log.OutputConfig) (zapcore.Core, zap.AtomicLevel, error) {
	decoder := &log.Decoder{OutputConfig: &config, Core: zapcore.NewNopCore(), ZapLevel: zap.NewAtomicLevel()}

	if err := w.factory.Setup(w.name, decoder); err != nil {
		return nil, zap.NewAtomicLevel(), fmt.Errorf("setting up %s failed: %v", w.name, err)
	}
	return newParamsCore(decoder.Core), decoder.ZapLevel, nil
}

// Config wecube配置
type Config struct {
	BizID int `yaml:"biz_id"`
}

// getDecoderAndConf 解析日志配置
func getDecoderAndConf(configDec plugin.Decoder) (*log.Decoder, *log.OutputConfig, *Config, error) {
	if configDec == nil {
		return nil, nil, nil, errors.New("wecube writer decoder empty")
	}
	decoder, ok := configDec.(*log.Decoder)
	if !ok {
		return nil, nil, nil, errors.New("wecube writer log decoder type invalid")
	}

	conf := &log.OutputConfig{}
	if err := decoder.Decode(&conf); err != nil {
		return nil, nil, nil, err
	}

	var cfg Config
	if err := conf.RemoteConfig.Decode(&cfg); err != nil {
		return nil, nil, nil, err
	}
	return decoder, conf, &cfg, nil
}

// Factory wecube trpc 插件实现
type Factory struct {
	w io.Writer
}

// Type wecube trpc插件类型
func (p *Factory) Type() string {
	return pluginType
}

const (
	timeColumnName          = "time"
	timestampColumnName     = "timestamp"
	levelColumnName         = "level"
	traceIDColumnName       = "traceID"
	spanIDColumnName        = "SpanID"
	sampledColumnName       = "sampled"
	dataColumnName          = "data"
	callerColumnName        = "caller"
	paramsColumnName        = "params"
	workflowRunIDColumnName = "workflow_run_id"

	// 全景默认column
	bizIDColumnName          = "biz_id"
	reportIPColumnName       = "report_ip"
	reportModuleColumnName   = "report_module"
	reportHostRoleColumnName = "report_host_role"
)

var fixedColumnName = map[string]struct{}{
	timeColumnName:           {},
	timestampColumnName:      {},
	levelColumnName:          {},
	traceIDColumnName:        {},
	spanIDColumnName:         {},
	sampledColumnName:        {},
	dataColumnName:           {},
	callerColumnName:         {},
	paramsColumnName:         {},
	workflowRunIDColumnName:  {},
	bizIDColumnName:          {},
	reportIPColumnName:       {},
	reportModuleColumnName:   {},
	reportHostRoleColumnName: {},
}

type customTimeEncoder struct {
	zapcore.Encoder
}

func (e *customTimeEncoder) Clone() zapcore.Encoder {
	return &customTimeEncoder{
		Encoder: e.Encoder.Clone(),
	}
}

func (e *customTimeEncoder) EncodeEntry(ent zapcore.Entry, fields []zapcore.Field) (*buffer.Buffer, error) {
	t := ent.Time
	secField := zap.Int64(timeColumnName, t.Unix())
	fields = append(fields, secField)
	return e.Encoder.EncodeEntry(ent, fields)
}

func customCallerEncoder(caller zapcore.EntryCaller, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(caller.TrimmedPath())
}

func customTimeSecondEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendInt64(t.UnixMicro())
}

// getEncoder 生成zap wecube encoder
func getEncoder(cfg *Config) zapcore.Encoder {
	zapLoggerEncoderConfig := zapcore.EncoderConfig{
		TimeKey:          timestampColumnName,
		LevelKey:         levelColumnName,
		NameKey:          "logger",
		CallerKey:        callerColumnName,
		MessageKey:       dataColumnName,
		StacktraceKey:    "stacktrace",
		EncodeCaller:     customCallerEncoder,
		EncodeTime:       customTimeSecondEncoder,
		EncodeLevel:      zapcore.CapitalLevelEncoder,
		EncodeDuration:   zapcore.MillisDurationEncoder,
		LineEnding:       "\n",
		ConsoleSeparator: " ",
	}
	encoder := &customTimeEncoder{zapcore.NewJSONEncoder(zapLoggerEncoderConfig)}
	encoder.AddInt(bizIDColumnName, cfg.BizID)
	encoder.AddString(reportIPColumnName, weenv.GetInnerIp())
	encoder.AddString(reportModuleColumnName, weenv.Module())
	encoder.AddInt(reportHostRoleColumnName, weenv.GetSysMetaInfo().HostRole)
	return encoder
}

// Setup wecube实例初始化log output core
func (p *Factory) Setup(name string, configDec plugin.Decoder) error {
	decoder, conf, cfg, err := getDecoderAndConf(configDec)
	if err != nil {
		return err
	}

	w := p.w
	if w == nil {
		w = &CubeWriter{
			BizID: cfg.BizID,
		}
	}

	l := zap.NewAtomicLevelAt(log.Levels[conf.Level])
	encoder := getEncoder(cfg)

	c := zapcore.NewCore(
		encoder,
		zapcore.AddSync(w),
		l,
	)

	decoder.Core = c
	decoder.ZapLevel = l
	return nil
}

// CubeWriter wecube io.writer
type CubeWriter struct {
	BizID int
}

// Write wecube io.writer Write实现
func (w *CubeWriter) Write(p []byte) (n int, err error) {
	return len(p), cube.ReportRawData(w.BizID, string(p))
}
