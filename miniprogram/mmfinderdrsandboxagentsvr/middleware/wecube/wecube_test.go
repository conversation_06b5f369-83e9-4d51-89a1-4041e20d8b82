package wecube

import (
	"bytes"
	"testing"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/wego/wxg/weenv"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/goccy/go-json"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zapcore"
)

func TestLogFactory(t *testing.T) {
	mockNow := time.Date(2020, time.January, 0, 0, 0, 0, 0, time.UTC)
	patch := gomonkey.NewPatches()
	patch.ApplyFunc(time.Now, func() time.Time { return mockNow })
	patch.ApplyFunc(customCallerEncoder,
		func(caller zapcore.EntryCaller, enc zapcore.PrimitiveArrayEncoder) { enc.AppendString("mocked_path") })
	patch.ApplyFunc(weenv.GetInnerIp, func() string { return "0.0.0.0" })
	patch.ApplyFunc(weenv.<PERSON>, func() string { return "test_module" })
	patch.ApplyFunc(weenv.GetSysMetaInfo, func() *weenv.SysMetaInfo { return &weenv.SysMetaInfo{} })
	defer patch.Reset()

	var buf bytes.Buffer
	pname := "test_core_level_newer"
	log.RegisterCoreLevelNewer(pname, &writerFactory{name: pluginName, factory: &Factory{&buf}})
	cfg := log.OutputConfig{Writer: pname}
	log.Register(pname, log.NewZapLog([]log.OutputConfig{cfg}))

	type result struct {
		Level          string `json:"level"`
		Timestamp      int64  `json:"timestamp"`
		Time           int64  `json:"time"`
		Caller         string `json:"caller"`
		Data           string `json:"data"`
		BizID          int    `json:"biz_id"`
		ReportIP       string `json:"report_ip"`
		ReportModule   string `json:"report_module"`
		ReportHostRole int    `json:"report_host_role"`
		Params         string `json:"params"`
		WorkflowRunID  string `json:"workflow_run_id"`
	}

	cases := []struct {
		fields []log.Field
		data   string
		expect any
	}{
		{nil, "msg1", result{
			Level: "INFO", Timestamp: 1577750400000000, Caller: "mocked_path", Data: "msg1",
			ReportIP: "0.0.0.0", ReportModule: "test_module", Time: 1577750400,
		}},
		{[]log.Field{{Key: "test", Value: 1}, {Key: "test21", Value: "data2"}}, "msg2", result{
			Level: "INFO", Timestamp: 1577750400000000, Caller: "mocked_path", Data: "msg2",
			ReportIP: "0.0.0.0", ReportModule: "test_module", Time: 1577750400,
			Params: `{"test":1,"test21":"data2"}`,
		}},
		{[]log.Field{{Key: "test2", Value: map[string]int{"a": 1, "b": 2}}, {Key: "test22", Value: 0.11}}, "msg3", result{
			Level: "INFO", Timestamp: 1577750400000000, Caller: "mocked_path", Data: "msg3",
			ReportIP: "0.0.0.0", ReportModule: "test_module", Time: 1577750400,
			Params: `{"test2":{"a":1,"b":2},"test22":0.11}`,
		}},
		{[]log.Field{{Key: "test3", Value: []int{1, 2, 3}}, {Key: "test23", Value: false}}, "msg4", result{
			Level: "INFO", Timestamp: 1577750400000000, Caller: "mocked_path", Data: "msg4",
			ReportIP: "0.0.0.0", ReportModule: "test_module", Time: 1577750400,
			Params: `{"test3":[1,2,3],"test23":false}`,
		}},
		{[]log.Field{{Key: "test4", Value: 'c'}, {Key: "test24", Value: struct{ S string }{"name"}}}, "msg5", result{
			Level: "INFO", Timestamp: 1577750400000000, Caller: "mocked_path", Data: "msg5",
			ReportIP: "0.0.0.0", ReportModule: "test_module", Time: 1577750400,
			Params: `{"test4":99,"test24":{"S":"name"}}`,
		}},
		{[]log.Field{
			{Key: "test5", Value: 'c'},
			{Key: "workflow_run_id", Value: "abc"},
		}, "msg5", result{
			Level: "INFO", Timestamp: 1577750400000000, Caller: "mocked_path", Data: "msg5",
			ReportIP: "0.0.0.0", ReportModule: "test_module", Time: 1577750400,
			Params: `{"test5":99}`, WorkflowRunID: "abc",
		}},
	}

	for _, c := range cases {
		l := log.Get(pname).With(c.fields...)
		l.Info(c.data)
		data := buf.Bytes()
		buf.Reset()
		var body result
		require.NoError(t, json.Unmarshal(data, &body))
		require.Equal(t, body, c.expect)
	}
}
