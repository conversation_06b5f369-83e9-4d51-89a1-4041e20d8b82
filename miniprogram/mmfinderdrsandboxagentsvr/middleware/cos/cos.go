// Package cos 对象存储操作
package cos

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/tencentyun/cos-go-sdk-v5"
)

// UploadBytes 直接上传字节数据到 cos
func UploadBytes(
	ctx context.Context,
	objectKey string,
	data []byte,
	contentType string,
) (objectURL string, err error) {
	// 创建 cos 客户端
	bucketURL := "http://mmfinderdrsandbox-1258344707.cos-internal.ap-shanghai.tencentcos.cn"
	u, _ := url.Parse(bucketURL)
	su, _ := url.Parse("http://cos-internal.ap-shanghai.tencentcos.cn")
	b := &cos.BaseURL{BucketURL: u, ServiceURL: su}
	c := cos.NewClient(b, &http.Client{
		Timeout: 100 * time.Second,
		Transport: &cos.AuthorizationTransport{
			SecretID:  "AKIDgBSWLyNYVFk15vWt64nGYfqy02sUr0vc",
			SecretKey: "VmpeEahaGhtvkKhqkPpMflcEb9SYQ3QZ",
		},
	})
	// 上传文件到 cos
	options := &cos.ObjectPutOptions{}
	options.ObjectPutHeaderOptions = &cos.ObjectPutHeaderOptions{
		ContentType: contentType,
	}
	reader := bytes.NewReader(data)
	_, err = c.Object.Put(context.Background(), objectKey, reader, options)
	if err != nil {
		return
	}
	// 拼接图片 URL
	objectURL = fmt.Sprintf("%s/%s", bucketURL, objectKey)
	log.WithContext(
		ctx,
		log.Field{Key: "object_url", Value: objectURL},
	).Info("上传图片至 cos 结束")
	return
}
