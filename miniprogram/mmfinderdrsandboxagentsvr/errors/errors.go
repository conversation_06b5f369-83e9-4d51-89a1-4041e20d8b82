// Package errors 自定义错误
package errors

import (
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/errs"
)

func newErr(code int32, msg string) *Error {
	return &Error{code, msg}
}

var (
	// InternalError 内部错误
	InternalError = newErr(10000, "InternalError")
	// BadInput 非法的输入
	BadInput = newErr(10001, "BadInput")
	// BadRetCode retcode非预期
	BadRetCode = newErr(10002, "BadRetCodeError")
	// ScriptExecFailed 脚本执行失败
	ScriptExecFailed = newErr(10003, "ScriptExecFailed")
	// BadResponse 返回结果非预期
	BadResponse = newErr(10004, "BadResponse")
	// ReqFailed 请求失败
	ReqFailed = newErr(10005, "ReqFailedError")

	// Interrupt 执行中断
	Interrupt = newErr(20001, "Interrupt")
)

// Error 自定义错误
type Error struct {
	Code int32
	msg  string
}

// CheckSameCode 判断 error code 是否相同
func (e Error) CheckSameCode(err error) bool {
	if err == nil {
		return false
	}
	err1, ok := err.(*errs.Error)
	if !ok {
		return false
	}
	return err1.Code == e.Code
}

// WithError 附带错误信息的 error
func (e Error) WithError(err error) error {
	return e.WithMessage(err.Error())
}

// WithMessage 附带文字信息的 error
func (e Error) WithMessage(msg string) error {
	message := e.msg
	if len(msg) > 0 {
		message = e.msg + ": " + msg
	}
	return errs.New(int(e.Code), message)
}

// WithMessagef 附带文字信息的 error
func (e Error) WithMessagef(pattern string, args ...any) error {
	message := e.msg
	if len(pattern) > 0 {
		message = e.msg + ": " + fmt.Sprintf(pattern, args...)
	}
	return errs.New(int(e.Code), message)
}
