// Package util 通常的工具方法
package util

import (
	"math/rand"
	"unsafe"
)

// RandomID 获取指定长度的随机字符串
func RandomID(size int) string {
	letters := "0123456789abcdef"
	b := make([]byte, size)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}

// UnsafeGetBytes str -> []byte
func UnsafeGetBytes(s string) []byte {
	return unsafe.Slice(unsafe.StringData(s), len(s))
}

// UnsafeGetString []byte -> str
func UnsafeGetString(bs []byte) string {
	return unsafe.String(unsafe.SliceData(bs), len(bs))
}
