// Package util http 相关的工具方法
package util

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"mmfinderdrsandboxagentsvr/errors"
)

// CommonProcessAPI 接口处理的通用包装函数
func CommonProcessAPI[T any, R any](
	w http.ResponseWriter,
	r *http.Request,
	req *T,
	handlerFunc func(context.Context, *T) (*R, error),
	description string,
) {
	// 0. 处理 context，request 和 response
	ctx := r.Context()
	if err := json.NewDecoder(r.Body).Decode(req); err != nil {
		WriteHTTPResponse(
			w,
			errors.BadInput.Code,
			fmt.Sprintf("请求体解析失败: error=%v", err),
			http.StatusBadRequest,
		)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	// 1. 处理业务逻辑
	resp, err := handlerFunc(ctx, req)
	CommonProcessAPIResponse(w, r, resp, err, description)
}

// CommonProcessAPIResponse 接口 Response 处理的通用包装函数
func CommonProcessAPIResponse(
	w http.ResponseWriter,
	r *http.Request,
	resp any,
	err error,
	description string,
) {
	// 1 调用业务逻辑出现错误，返回错误响应
	if err != nil {
		WriteHTTPResponse(
			w,
			errors.InternalError.Code,
			fmt.Sprintf("%s失败: error=%v", description, err),
			http.StatusInternalServerError,
		)
		return
	}
	// 2 返回成功响应
	if err := json.NewEncoder(w).Encode(resp); err != nil {
		WriteHTTPResponse(
			w,
			errors.InternalError.Code,
			fmt.Sprintf("无法编码响应: error=%v", err),
			http.StatusInternalServerError,
		)
		return
	}
}

// WriteHTTPResponse 给 http
func WriteHTTPResponse(w http.ResponseWriter, code int32, message string, statusCode int) {
	w.WriteHeader(statusCode)
	_ = json.NewEncoder(w).Encode(map[string]interface{}{
		"code":    code,
		"message": message,
	})
}
