// Package util 操作系统相关的工具方法
package util

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"
)

var lock sync.Mutex

// ExecuteFunctionInQueueWithTimeout 以并发度为 1 的方式执行 protectedOperation
// timeout 仅仅是针对被锁 block 住的时间，不包含 protectedOperation 的执行耗时
func ExecuteFunctionInQueueWithTimeout(timeout time.Duration, protectedOperation func()) error {
	deadline := time.Now().Add(timeout)
	for {
		if lock.TryLock() {
			defer lock.Unlock()
			protectedOperation()
			return nil
		} else if time.Now().After(deadline) {
			return fmt.Errorf("timeout")
		}
		time.Sleep(100 * time.Millisecond)
	}
}

// ReadFile 读取文件
func ReadFile(path string) ([]byte, error) {
	var data []byte
	var err error
	var resp *http.Response
	var file *os.File
	// 判断是否是 URL（包含 http:// 或 https://）
	if strings.HasPrefix(path, "http://") || strings.HasPrefix(path, "https://") {
		// 处理 HTTP URL
		resp, err = http.Get(path)
		if err != nil {
			return nil, fmt.Errorf("HTTP 请求失败，path=%s，error=%v", path, err)
		}
		defer resp.Body.Close()
		if resp.StatusCode != http.StatusOK {
			return nil, fmt.Errorf("HTTP 请求失败，path=%s，status_code=%v", path, resp.StatusCode)
		}
		// 读取完整响应体
		data, err = io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("读取 HTTP 响应体失败，path=%s，error=%v", path, err)
		}
	} else {
		// 处理本地文件路径
		file, err = os.Open(path)
		if err != nil {
			return nil, fmt.Errorf("打开本地文件失败，path=%s，error=%v", path, err)
		}
		defer file.Close()
		// 读取文件内容
		data, err = io.ReadAll(file)
		if err != nil {
			return nil, fmt.Errorf("读取本地文件内容失败，path=%s，error=%v", path, err)
		}
	}
	// 验证数据有效性
	if len(data) == 0 {
		return nil, fmt.Errorf("获取的数据为空，path=%s", path)
	}
	return data, nil
}

// ReadFileList 批量读取文件
func ReadFileList(pathList []string) ([][]byte, error) {
	var result [][]byte
	for _, path := range pathList {
		data, err := ReadFile(path)
		if err != nil {
			return nil, err
		}
		result = append(result, data)
	}
	return result, nil
}
