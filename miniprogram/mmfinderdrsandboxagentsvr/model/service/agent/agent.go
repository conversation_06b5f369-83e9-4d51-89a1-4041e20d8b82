// Package agent 定义相关的 service 类型
package agent

import (
	"encoding/json"
)

// PythonResult 推理脚本的返回
type PythonResult struct {
	Answers            []string                      `json:"answers"`
	AnswersRawData     []string                      `json:"answers_raw_data"`
	Interrupt          map[string]json.RawMessage    `json:"interrupt"`
	LogData            []map[string]json.RawMessage  `json:"log_data"`
	Screens            []string                      `json:"screens"`
	StandardOutputList []SandboxActionStandardOutput `json:"standard_output_list"`
	Status             string                        `json:"status"`
	TargetID           string                        `json:"target_id"`
}

// PythonResultDTO 推理脚本返回的二次封装
type PythonResultDTO struct {
	PythonResult *PythonResult `json:"python_result"`
	SharedData   string        `json:"shared_data"`
}

// SandboxActionStandardOutput 沙箱操作的标准输出
type SandboxActionStandardOutput struct {
	AppletPageInfo      map[string]json.RawMessage               `json:"applet_page_info"`
	DomXML              map[string]json.RawMessage               `json:"dom_xml"`
	ElementsRects       map[string]json.RawMessage               `json:"elements_rects"`
	NativeElementsRects map[string]json.RawMessage               `json:"native_elements_rects"`
	ScreenshotDTO       SandboxActionStandardOutputScreenshotDTO `json:"screenshot_dto"`
}

// SandboxActionStandardOutputScreenshotDTO 沙箱操作的标准输出截图的输出
type SandboxActionStandardOutputScreenshotDTO struct {
	File        string `json:"file"`
	RealSize    []int  `json:"real_size"`
	ResizedSize []int  `json:"resized_size"`
	URL         string `json:"url"`
}
