// Package sandbox 定义相关的 service 类型
package sandbox

import (
	"mmfinderdrsandboxagentsvr/model/api/base"
)

// GetWechatClientsRespData 定义相关的 service 类型
type GetWechatClientsRespData struct {
	ID       int             `json:"id"`
	DateTime base.CustomTime `json:"datetime"`
	Token    string          `json:"token"`
	Status   int             `json:"status"`
}

// GetWechatClientsResp 定义相关的 service 类型
type GetWechatClientsResp struct {
	Data []*GetWechatClientsRespData `json:"data"`
}

// WechatCDPBaseEvent 定义相关的 service 类型
type WechatCDPBaseEvent struct {
	ID    *uint32 `json:"id"`
	Event string  `json:"event"`
}

// XWebMethodArray 事件类型分为仅输入，仅输出和输入输出都有
var XWebMethodArray = [...]string{
	"XWeb.LaunchApplet",        // 输入
	"XWeb.OnLaunchApplet",      // 输出
	"XWeb.OnAppletReady",       // 输出
	"XWeb.CloseApplet",         // 输入，输出
	"XWeb.targets",             // 输入，输出
	"XWeb.inspect",             // 输入，输出
	"XWeb.NetLogStart",         // 输入，输出
	"XWeb.NetLogStop",          // 输入，输出
	"XWeb.SetLocation",         // 输入
	"XWeb.OnShareAppMessage",   // 输出
	"XWeb.OnJsApiCallback",     // 输出
	"XWeb.RequestLogin",        // 输入
	"XWeb.OnLoginResult",       // 输出
	"XWeb.OnLoginFinish",       // 输出
	"XWeb.RequestLogout",       // 输入
	"XWeb.OnLogoutResult",      // 输出
	"XWeb.GetAppletPageInfo",   // 输入
	"XWeb.OnGetAppletPageInfo", // 输出
}
