// Package base 定义接口的基本类型
package base

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"

	"mmfinderdrsandboxagentsvr/errors"
)

// Resp 所有接口返回的基类
type Resp[T any] struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    *T     `json:"data"`
}

// SetErrs 设置返回的 code 和 message
func (b *Resp[T]) SetErrs(ctx context.Context, err error) {
	if err == nil {
		b.Code = 0
		return
	}

	b.Code = int(errors.InternalError.Code)
	b.Message = err.Error()
	e, ok := err.(*errs.Error)
	if ok {
		b.Code = int(e.Code)
		b.Message = e.Msg
	}
	log.WithContext(
		ctx,
		log.Field{Key: "code", Value: b.Code},
		log.Field{Key: "error", Value: err},
	).Error("接口返回错误")
}

// SetErr 设置返回的 code 和 message
func (b *Resp[T]) SetErr(ctx context.Context, code int, err error) {
	b.Code = code
	if code != 0 {
		log.WithContext(
			ctx,
			log.Field{Key: "code", Value: code},
			log.Field{Key: "error", Value: err},
		).Error("接口返回错误")
	}
	if err != nil {
		b.Message = err.Error()
	}
}

// CustomTime 自定义的时间类型
type CustomTime time.Time

func (ct *CustomTime) String() string {
	t := time.Time(*ct)
	return t.Format("2006-01-02 15:04:05")
}

// MarshalJSON 序列化
func (ct *CustomTime) MarshalJSON() ([]byte, error) {
	return []byte(fmt.Sprintf("%q", ct.String())), nil
}

// UnmarshalJSON 反序列化
func (ct *CustomTime) UnmarshalJSON(data []byte) (err error) {
	tmp, err := time.Parse("2006-01-02 15:04:05", strings.Trim(string(data), "\""))
	*ct = CustomTime(tmp)
	return
}
