// Package sandbox 定义相关的接口类型
package sandbox

// AuthCodeRespData 定义相关的接口类型
type AuthCodeRespData struct {
	ILinkAuthCode string `json:"ilink_auth_code"`
}

// CDPProxyReq 定义相关的接口类型
type CDPProxyReq struct {
	TargetID *string                `json:"target_id"`
	Method   string                 `json:"method"`
	Params   map[string]interface{} `json:"params"`
}

// CDPProxyRespData 定义相关的接口类型
type CDPProxyRespData struct {
	ID     int64                  `json:"id"`
	Event  string                 `json:"event"`
	Data   interface{}            `json:"data"`
	Result map[string]interface{} `json:"result"`
}

// UserCliRespData 定义相关的接口类型
type UserCliRespData struct {
	ID int64  `json:"id"`
	IP string `json:"ip"`
}
