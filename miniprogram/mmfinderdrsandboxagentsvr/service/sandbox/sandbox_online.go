// Package sandbox 在线沙箱
package sandbox

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"

	"mmfinderdrsandboxagentsvr/model/api/base"
	sandbox_api_model "mmfinderdrsandboxagentsvr/model/api/sandbox"
)

// Online 在线沙箱的实体类
type Online struct {
	HeartbeatStopFlag bool
	WechatProxy       *WechatProxy

	svrkitHelperClient http.Client
}

// SandboxOnlineObj 一个单例的实体类对象
var SandboxOnlineObj *Online

// InitSandboxOnline 初始化
func InitSandboxOnline() {
	SandboxOnlineObj = NewSandboxOnline()
}

// NewSandboxOnline 创建一个单例的实体类对象
func NewSandboxOnline() (s *Online) {
	s = &Online{
		svrkitHelperClient: http.NewClientProxy("trpc.http.mmfinderdrsandboxsvrkithelper.common"),
	}
	// 1. 确保微信启动
	s.ensureWechatStarted()
	// 2. 启动微信链接
	s.WechatProxy = NewWechatProxy()
	log.Info("启动微信链接成功")
	return
}

// ExecuteInstruction 操作微信
func (s *Online) ExecuteInstruction(
	ctx context.Context,
	req *sandbox_api_model.CDPProxyReq,
) (resp *base.Resp[sandbox_api_model.CDPProxyRespData], err error) {
	return s.WechatProxy.ExecuteInstruction(ctx, req)
}

// Exit 优雅退出程序
func (s *Online) Exit() {
	s.WechatProxy.Exit()
}

// GetAuthCode 获取认证码
func (s *Online) GetAuthCode(
	ctx context.Context,
	uinStr string,
) (sandbox_api_model.AuthCodeRespData, error) {
	var res sandbox_api_model.AuthCodeRespData
	uin, err := strconv.ParseUint(uinStr, 10, 64)
	if err != nil {
		return res, fmt.Errorf("uin 格式错误: %v", err)
	}
	req := struct {
		Uin    uint64 `json:"uin"`
		TaskID string `json:"task_id"`
	}{Uin: uin, TaskID: strconv.FormatInt(time.Now().UnixMilli(), 10)}
	if err := s.svrkitHelperClient.Post(ctx, "/api/v1/get_ilink_auth", &req, &res); err != nil {
		return res, fmt.Errorf("调用认证服务失败: %v", err)
	}
	return res, nil
}

// GetShareURLData 获取小程序分享链接的信息，返回是一个 json 字符串
func (s *Online) GetShareURLData(ctx context.Context, targetID string) (data string, err error) {
	// 1. 需要先清理所有管道无用的堆积消息
	s.WechatProxy.CleanAllWechatXWebInstructionChannel(ctx)
	// 2. 执行小程序操作模拟点击分享按钮
	jsCode := `"use strict";` +
		`(()=>{function n(){let e=document.querySelector("#capsule .capsule_icon.left_icon");` +
		`e.click();` +
		`let t=document.querySelector("#system_menu > div.item_list.main > div.menu_item:nth-child(1)");` +
		`t.textContent?.trim()==="\u8F6C\u53D1\u7ED9\u670B\u53CB"?t.click():e.click()}n();})();`
	req := &sandbox_api_model.CDPProxyReq{
		TargetID: &targetID,
		Method:   "Runtime.evaluate",
		Params: map[string]interface{}{
			"expression":    string(jsCode),
			"returnByValue": true,
		},
	}
	// 3. 等待 XWeb 回调事件消息来获取分享链接，这里的 data 是一个 json 字符串
	_, err = s.ExecuteInstruction(ctx, req)
	if err == nil {
		resp, err := s.WechatProxy.ExecuteOnShareAppMessageCallback(ctx)
		if err == nil {
			dataMap, _ := resp.Data.Data.(map[string]interface{})
			data, _ = dataMap["data"].(string)
		}
	}
	return
}

// Login 操作微信登陆
func (s *Online) Login(
	ctx context.Context,
	authCode string,
) (resp *base.Resp[sandbox_api_model.CDPProxyRespData], err error) {
	req := &sandbox_api_model.CDPProxyReq{
		Method: "XWeb.RequestLogin",
		Params: map[string]interface{}{
			"role":      "server",
			"auth_code": authCode,
		},
	}
	return s.ExecuteInstruction(ctx, req)
}

// Logout 操作微信登出
func (s *Online) Logout(
	ctx context.Context,
) (resp *base.Resp[sandbox_api_model.CDPProxyRespData], err error) {
	req := &sandbox_api_model.CDPProxyReq{
		Method: "XWeb.RequestLogout",
		Params: map[string]interface{}{
			"role": "server",
		},
	}
	return s.ExecuteInstruction(ctx, req)
}

// SetLocation 操作微信设置地理位置
func (s *Online) SetLocation(
	ctx context.Context,
	latitude float64,
	longitude float64,
) (resp *base.Resp[sandbox_api_model.CDPProxyRespData], err error) {
	if latitude < 0 || longitude < 0 {
		latitude = 23.098903
		longitude = 113.324917
	}
	req := &sandbox_api_model.CDPProxyReq{
		Method: "XWeb.SetLocation",
		Params: map[string]interface{}{
			"role":      "server",
			"latitude":  latitude,
			"longitude": longitude,
		},
	}
	return s.ExecuteInstruction(ctx, req)
}

func (s *Online) waitForWechatStarted() (err error) {
	begin := time.Now()
	timeout := time.Second * 30
	for {
		if _, err = GetWechatClient(); err != nil {
			if time.Since(begin) > timeout {
				log.Error("wechat 探活失败")
				return
			}
			time.Sleep(time.Millisecond * 100)
			continue
		}
		break
	}
	return
}

func (s *Online) ensureWechatStarted() {
	for {
		err := s.waitForWechatStarted()
		if err != nil {
			log.With(log.Field{Key: "error", Value: err}).Error("启动微信等待失败")
			time.Sleep(time.Second)
			continue
		}
		break
	}
}
