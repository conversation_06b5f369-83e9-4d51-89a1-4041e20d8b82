package agent

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"

	agent_service_model "mmfinderdrsandboxagentsvr/model/service/agent"
)

func TestFindAvailableJSON(t *testing.T) {
	cases := []struct {
		input    string
		prefix   []byte
		reverse  bool
		expected string
	}{
		{
			"\n\n{\"code\": -1, \"msg\": \"用户中断\"}\nTraceback\n  File\n    res =\n          ^\n\n",
			nil, true,
			"{\"code\": -1, \"msg\": \"用户中断\"}",
		},
		{
			"\n{\"code\":0,\"msg\":\"abc\"}\nbody\001{\"code\": -1, \"msg\": \"用户中断\"}\nTraceback\n  File\n  res =\n ^\n\n",
			[]byte("body\001"), true,
			"{\"code\": -1, \"msg\": \"用户中断\"}",
		},
		{
			"\n\"msg\":\"abc\"}\n{\"code\": -1, \"msg\nTraceback\n  File\n  res =\n ^\n\n",
			nil, true,
			"",
		},
		{
			"\n{\"code\":0,\"msg\":\"abc\"}\n{\"code\": -1, \"msg\": \"用户中断\"}\nTraceback\n  File\n  res =\n ^\n\n",
			nil, false,
			"{\"code\":0,\"msg\":\"abc\"}",
		},
		{
			"\n{\"code\":0,\"msg\":\"abc\"}\n{\"code\": -1, \"msg\": \"用户中断\"}\nTraceback\n  File\n  res =\n ^\n\n",
			nil, true,
			"{\"code\": -1, \"msg\": \"用户中断\"}",
		},
	}

	for i, tc := range cases {
		result, err := findAvailableJSON([]byte(tc.input), tc.prefix, tc.reverse)
		if tc.expected == "" {
			require.Error(t, err, "case %d", i)
			continue
		}
		require.NoError(t, err, "case %d", i)
		require.Equal(t, tc.expected, string(result), "case %d", i)
	}
}

func TestProcessScreenshots(t *testing.T) {
	// mock 数据
	standardOutput := agent_service_model.SandboxActionStandardOutput{
		AppletPageInfo:      map[string]json.RawMessage{},
		DomXML:              map[string]json.RawMessage{},
		ElementsRects:       map[string]json.RawMessage{},
		NativeElementsRects: map[string]json.RawMessage{},
		ScreenshotDTO: agent_service_model.SandboxActionStandardOutputScreenshotDTO{
			File:        "/home/<USER>/workspace/1.jpeg",
			RealSize:    []int{393, 852},
			ResizedSize: []int{410, 889},
			URL: "http://mmfinderdrsandbox-1258344707.cos-internal.ap-shanghai.tencentcos.cn/" +
				"mmfinderdrsandboxagentsvr/online-995/data_collection/wx3dcca19d0aa51755/2895060521/" +
				"20250630_13/60a0b1f09b48446f8481b66655f3c96c/" +
				"6_standard_click.jpg",
		},
	}
	standardOutputList := []agent_service_model.SandboxActionStandardOutput{standardOutput}

	// 3. 调用被测函数
	longURL, uniqueURL, screenshotURLs, errCode, err := processScreenshots(context.Background(), standardOutputList)
	if err != nil {
		t.Fatalf("processScreenshots 失败: errCode=%d, err=%v", errCode, err)
	}

	// 4. 验证结果
	fmt.Println("测试结果:")
	fmt.Printf("长图URL: %s\n", longURL)
	fmt.Printf("去重长图URL: %s\n", uniqueURL)
	fmt.Printf("截图URL列表: %v\n", screenshotURLs)

	// 验证返回的URL数量
	if len(screenshotURLs) != 3 {
		t.Errorf("期望返回3个截图URL, 实际返回%d个", len(screenshotURLs))
	}

	// 验证长图哈希不为空
	if longURL == "" {
		t.Error("长图URL不应为空")
	}

	// 验证去重长图URL不为空
	if uniqueURL == "" {
		t.Error("去重长图URL不应为空")
	}
}
