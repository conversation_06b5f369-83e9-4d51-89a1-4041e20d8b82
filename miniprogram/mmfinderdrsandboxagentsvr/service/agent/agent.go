// Package agent 执行 agent 推理算法
package agent

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"image"
	"image/jpeg"
	"net/url"
	"os"
	"os/exec"
	"slices"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/wego/wxg/weenv"

	"mmfinderdrsandboxagentsvr/errors"
	"mmfinderdrsandboxagentsvr/middleware/cos"
	agent_api_model "mmfinderdrsandboxagentsvr/model/api/agent"
	"mmfinderdrsandboxagentsvr/model/api/base"
	agent_service_model "mmfinderdrsandboxagentsvr/model/service/agent"
	"mmfinderdrsandboxagentsvr/service/sandbox"
	"mmfinderdrsandboxagentsvr/util"
)

// Run 执行 agent 推理算法
func Run(
	ctx context.Context,
	authCode string,
	position *agent_api_model.PositionInfo,
	skipShareURLData bool,
	args []string,
	envs []string,
) (resp *base.Resp[agent_api_model.BaseRespData], err error) {
	resp = &base.Resp[agent_api_model.BaseRespData]{
		Data: &agent_api_model.BaseRespData{},
	}
	// 1. 执行 Python 脚本并解析结果
	pyResultDTO, err := RunAgentInferPython(ctx, authCode, position, skipShareURLData, args, envs)
	if err != nil {
		resp.SetErr(ctx, 1, err)
		return
	}
	thinkingBytes, err := json.Marshal(pyResultDTO.PythonResult)
	if err != nil {
		resp.SetErr(ctx, 2, err)
	}
	resp.Data.ShareURL = fmt.Sprintf("http://%s:%d+%s+%s+%s", weenv.GetInnerIp(), 80, "0", "fixrequestid", "thinkingStr")
	resp.Data.Thinking = []string{string(thinkingBytes)}
	// 2. 合并图片变成一张长图片，并将长图和原始图片都上传至 COS
	longImgURL, longUniqueImgURL, screenshotImgURLs, errCode, err :=
		processScreenshots(ctx, pyResultDTO.PythonResult.StandardOutputList)
	if err != nil {
		resp.SetErr(ctx, errCode, err)
		return
	}
	resp.Data.LongImgURL = longImgURL
	resp.Data.LongUniqueImgURL = longUniqueImgURL
	resp.Data.ScreenshotImgURLs = screenshotImgURLs
	// 3. 拿到的分享数据需要进行二次转换做适配
	updatedData, err := processSharedData(ctx, pyResultDTO.SharedData)
	if err != nil {
		resp.SetErr(ctx, 5, err)
		return
	}
	resp.Data.DebugStr = updatedData
	return
}

// RunAgentInferPython 以胶水的方式调用 python
func RunAgentInferPython(
	ctx context.Context,
	authCode string,
	position *agent_api_model.PositionInfo,
	skipShareURLData bool,
	reqArgs []string,
	envs []string,
) (*agent_service_model.PythonResultDTO, error) {
	res := &agent_service_model.PythonResultDTO{
		PythonResult: &agent_service_model.PythonResult{},
	}
	if authCode != "" && position != nil {
		// 1. 初始化沙箱环境
		_, _ = sandbox.SandboxOnlineObj.Logout(ctx)
		if _, err := sandbox.SandboxOnlineObj.Login(ctx, authCode); err != nil {
			return res, errors.ReqFailed.WithError(err)
		}
		defer func() {
			_, _ = sandbox.SandboxOnlineObj.Logout(ctx)
		}()
		if _, err := sandbox.SandboxOnlineObj.SetLocation(ctx, position.Latitude, position.Longitude); err != nil {
			return res, errors.ReqFailed.WithError(err)
		}
	}
	// 2. 准备 Python cmd 调用参数
	startTime := time.Now()
	stdout, stderr, err := execInferScript(ctx, reqArgs, envs)
	runTime := time.Since(startTime).String()
	// 3. 处理结果
	// 3.1 错误处理
	stderrStr := stderr.String()
	if err != nil {
		errMsg := "Python 脚本执行失败"
		log.WithContext(
			ctx,
			log.Field{Key: "run_time", Value: runTime},
			log.Field{Key: "stderr", Value: stderrStr},
			log.Field{Key: "error", Value: err},
		).Error(errMsg)
		return res, errors.ScriptExecFailed.WithMessagef(
			"%s：run_time=%s，stderr=%s，error=%v", errMsg, runTime, stderrStr, err,
		)
	}
	outputBytes := stdout.Bytes()
	// 3.2 正常处理
	log.WithContext(
		ctx,
		log.Field{Key: "run_time", Value: runTime},
		log.Field{Key: "stderr", Value: stderrStr},
		log.Field{Key: "stdout", Value: stdout.String()},
	).Info("Python 脚本完整输出")
	if err = processPythonResult(outputBytes, res); err != nil {
		log.WithContext(ctx,
			log.Field{Key: "error", Value: err},
			log.Field{Key: "body", Value: stdout.String()},
		).Error("Python 输出的 JSON 数据解析失败")
		return res, err
	}
	// 获取分享链接
	if !skipShareURLData {
		var data string
		data, err = sandbox.SandboxOnlineObj.GetShareURLData(ctx, res.PythonResult.TargetID)
		if err != nil {
			errMsg := "获取小程序分享链接失败"
			log.WithContext(
				ctx,
				log.Field{Key: "error", Value: err},
			).Error(errMsg)
			return res, errors.ReqFailed.WithMessagef("%s ,error=%v", errMsg, err)
		}
		res.SharedData = data
	}
	return res, nil
}

func execInferScript(ctx context.Context, reqArgs []string, envs []string) (bytes.Buffer, bytes.Buffer, error) {
	const exeDir = "/home/<USER>/mmfinderdrsandboxagentsvr/pyfile"
	args := append([]string{exeDir + "/main.py"}, reqArgs...)
	// 创建命令对象
	cmd := exec.CommandContext(ctx, "python3", args...)
	// 设置工作目录为 Python 脚本所在目录
	cmd.Dir = exeDir
	// 设置环境变量
	cmd.Env = append(os.Environ(), envs...)
	// 捕获输出
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout // 捕获标准输出用于解析
	cmd.Stderr = &stderr // 直接输出错误到控制台
	// 执行命令
	err := cmd.Run()
	return stdout, stderr, err
}

func findAvailableJSON(outputBytes []byte, prefix []byte, reverse bool) ([]byte, error) {
	lines := bytes.Split(outputBytes, []byte{'\n'})
	if reverse {
		slices.Reverse(lines)
	}
	var jsonData []byte
	for i := 0; i < len(lines); i++ {
		line := bytes.TrimSpace(lines[i])
		if len(line) == 0 {
			continue
		}
		if !bytes.HasPrefix(line, prefix) {
			continue
		}
		line = bytes.TrimPrefix(line, prefix)
		if bytes.HasPrefix(line, []byte{'{'}) && bytes.HasSuffix(line, []byte{'}'}) {
			jsonData = line
			break
		}
	}
	if len(jsonData) == 0 {
		return nil, errors.BadResponse.WithMessage("无法在 Python 输出中找到有效的 JSON 数据")
	}
	return jsonData, nil
}

func processPythonResult(outputBytes []byte, res *agent_service_model.PythonResultDTO) error {
	jsonData, err := findAvailableJSON(outputBytes, []byte{}, true)
	if err != nil {
		return err
	}
	// 解析 Python 输出的 JSON 数据
	if err = json.Unmarshal(jsonData, res.PythonResult); err != nil {
		return errors.BadResponse.WithMessagef(
			"Python 输出的 JSON 数据解析失败：json_data=%s，error=%s", string(jsonData), err,
		)
	}
	return nil
}

func processScreenshots(
	ctx context.Context, standardOutputList []agent_service_model.SandboxActionStandardOutput,
) (string, string, []string, int, error) {
	screenshotFileList := []string{}
	screenshotURLList := []string{}
	for _, standardOutput := range standardOutputList {
		screenshotFileList = append(screenshotFileList, standardOutput.ScreenshotDTO.File)
		screenshotURLList = append(screenshotURLList, standardOutput.ScreenshotDTO.URL)
	}
	if len(screenshotURLList) == 0 {
		return "", "", []string{}, 0, nil
	}
	firstScreenshotURL := screenshotURLList[0]
	if len(screenshotURLList) == 1 {
		return firstScreenshotURL, firstScreenshotURL, screenshotURLList, 0, nil
	}
	// 根据 url 获取 objectKeyPrefix
	urlObj, err := url.Parse(firstScreenshotURL)
	if err != nil {
		return "", "", screenshotURLList, 3, err
	}
	objectKeyPrefix := urlObj.Path[1 : strings.LastIndex(urlObj.Path, "/")+1]
	// 将截图转换为 image.Image 对象
	imgList, imgHashList, err := util.GenerateImagesByURL(screenshotFileList)
	if err != nil {
		return "", "", screenshotURLList, 4, err
	}
	// 拼接图片
	longImgURL := ""
	longUniqueImgURL := ""
	// 上传长图片
	img := util.ConcatImagesVertically(imgList)
	if img != nil {
		var buffer bytes.Buffer
		if err := jpeg.Encode(&buffer, img, &jpeg.Options{Quality: 100}); err == nil {
			if objectURL, err := cos.UploadBytes(
				ctx,
				fmt.Sprintf("%slong_img.jpg", objectKeyPrefix),
				buffer.Bytes(),
				"image/jpg",
			); err == nil {
				longImgURL = objectURL
			}
		}
	}
	// 上传去重的长图片
	tempList := make([]string, 0, len(imgList))
	uniqueImgList := make([]image.Image, 0, len(imgList))
	for index, imgHash := range imgHashList {
		if slices.Contains(tempList, imgHash) {
			continue
		}
		tempList = append(tempList, imgHash)
		uniqueImgList = append(uniqueImgList, imgList[index])
	}
	if len(uniqueImgList) == len(imgList) {
		longUniqueImgURL = longImgURL
	} else {
		uniqueImg := util.ConcatImagesVertically(uniqueImgList)
		if uniqueImg != nil {
			var buffer bytes.Buffer
			if err := jpeg.Encode(&buffer, uniqueImg, &jpeg.Options{Quality: 100}); err == nil {
				if objectURL, err := cos.UploadBytes(
					ctx,
					fmt.Sprintf("%slong_unique_img.jpg", objectKeyPrefix),
					buffer.Bytes(),
					"image/jpg",
				); err == nil {
					longUniqueImgURL = objectURL
				}
			}
		}
	}
	return longImgURL, longUniqueImgURL, screenshotURLList, 0, nil
}

func processSharedData(ctx context.Context, data string) (res string, err error) {
	if data == "" {
		return
	}
	// 1. 将 JSON 字符串解析为 map
	var dataMap map[string]interface{}
	if err = json.Unmarshal([]byte(data), &dataMap); err != nil {
		return
	}
	// 2. 提取 thumbIconPath 的值
	thumbIconPath, ok := dataMap["thumbIconPath"].(string)
	if !ok {
		err = fmt.Errorf("thumbIconPath not found or is not a string")
		return
	}
	// 3. 读取图片文件
	imageData, err := os.ReadFile(thumbIconPath)
	if err != nil {
		log.WithContext(
			ctx,
			log.Field{Key: "thumbIconPath", Value: thumbIconPath},
			log.Field{Key: "error", Value: err},
		).Warn("读取图片文件失败，将 thumbIconPath 置空")
		dataMap["thumbIconPath"] = "" // 置空字段
	} else {
		// 对图片内容进行 Base64 编码
		encoded := base64.StdEncoding.EncodeToString(imageData)
		dataMap["thumbIconPath"] = encoded // 更新字段
	}
	// 4. 将 map 重新序列化为 JSON 字符串
	updatedData, err := json.Marshal(dataMap)
	if err != nil {
		return
	}
	res = string(updatedData)
	return
}
