// Package sandbox 定义标注相关的接口
package sandbox

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	sandbox_api_model "mmfinderdrsandboxagentsvr/model/api/sandbox"
	"mmfinderdrsandboxagentsvr/service/annotation"
	"mmfinderdrsandboxagentsvr/util"
)

// createSandbox 创建沙箱接口
func createSandbox(w http.ResponseWriter, r *http.Request) {
	util.CommonProcessAPI(
		w,
		r,
		&sandbox_api_model.CreateSandboxReq{},
		annotation.CreateSandbox,
		"创建沙箱",
	)
}

// sandboxAction 执行沙箱操作接口
func sandboxAction(w http.ResponseWriter, r *http.Request) {
	util.CommonProcessAPI(
		w,
		r,
		&sandbox_api_model.ActionReq{},
		annotation.ExecuteSandboxAction,
		"执行沙箱操作",
	)
}

// killSandbox 关闭沙箱接口
func killSandbox(w http.ResponseWriter, r *http.Request) {
	util.CommonProcessAPI(
		w,
		r,
		&sandbox_api_model.KillSandboxReq{},
		annotation.KillSandbox,
		"关闭沙箱",
	)
}

// submitAnnotation 提交标注内容接口
func submitAnnotation(w http.ResponseWriter, r *http.Request) {
	util.CommonProcessAPI(
		w,
		r,
		&sandbox_api_model.SubmitAnnotationReq{},
		annotation.SubmitAnnotation,
		"提交标注内容",
	)
}

// parseRTXValues 解析 RTX 参数，支持逗号分隔的多个值
func parseRTXValues(r *http.Request) []string {
	rtxValues := make([]string, 0)
	rtxParam := r.URL.Query().Get("rtx") // 获取 URL 中的 rtx 参数
	if strings.Contains(rtxParam, ",") {
		// 如果包含逗号，则分割多个值
		values := strings.Split(rtxParam, ",")
		for _, v := range values {
			trimmed := strings.TrimSpace(v) // 去掉前后空白
			if trimmed != "" {
				rtxValues = append(rtxValues, trimmed)
			}
		}
	} else if rtxParam != "" {
		// 如果没有逗号，且 rtx 参数非空，则直接添加
		rtxValues = append(rtxValues, rtxParam)
	}
	return rtxValues
}

// parseIntParam 解析整数参数，如果解析失败则返回默认值
func parseIntParam(r *http.Request, paramName string, defaultValue int) int {
	paramStr := r.URL.Query().Get(paramName)
	if paramStr != "" {
		if parsed, err := strconv.Atoi(paramStr); err == nil {
			return parsed
		}
	}
	return defaultValue
}

// parseInt8PtrParam 解析可选的 int8 参数，返回指针类型
func parseInt8PtrParam(r *http.Request, paramName string) *int8 {
	paramStr := r.URL.Query().Get(paramName)
	if paramStr != "" {
		if parsed, err := strconv.Atoi(paramStr); err == nil {
			val := int8(parsed)
			return &val
		}
	}
	return nil
}

// parseTimeParams 解析时间范围参数
func parseTimeParams(r *http.Request) (*time.Time, *time.Time) {
	const timeLayout = "2006-01-02 15:04:05"
	var startTime, endTime *time.Time

	startTimeStr := r.URL.Query().Get("startTime")
	if startTimeStr != "" {
		if parsedTime, err := time.Parse(timeLayout, startTimeStr); err == nil {
			startTime = &parsedTime
		}
	}

	endTimeStr := r.URL.Query().Get("endTime")
	if endTimeStr != "" {
		if parsedTime, err := time.Parse(timeLayout, endTimeStr); err == nil {
			endTime = &parsedTime
		}
	}
	return startTime, endTime
}

// parseAnnotationIDs 解析 annotationIDs 参数，支持逗号分隔的多个值
func parseAnnotationIDs(r *http.Request) []int64 {
	annotationIDs := make([]int64, 0)
	annotationIDsParam := r.URL.Query().Get("annotationIds") // 获取 URL 中的 annotationIds 参数
	if strings.Contains(annotationIDsParam, ",") {
		// 如果包含逗号，则分割多个值
		values := strings.Split(annotationIDsParam, ",")
		for _, v := range values {
			trimmed := strings.TrimSpace(v) // 去掉前后空白
			if trimmed != "" {
				if id, err := strconv.ParseInt(trimmed, 10, 64); err == nil {
					annotationIDs = append(annotationIDs, id)
				}
			}
		}
	} else if annotationIDsParam != "" {
		// 如果没有逗号，且 annotationIDs 参数非空，则直接添加
		if id, err := strconv.ParseInt(annotationIDsParam, 10, 64); err == nil {
			annotationIDs = append(annotationIDs, id)
		}
	}
	return annotationIDs
}

// getAnnotations 获取标注内容列表接口
func getAnnotations(w http.ResponseWriter, r *http.Request) {
	// 0. 处理 context，request 和 response
	rtxValues := parseRTXValues(r)
	appID := r.URL.Query().Get("appId")
	targetID := r.URL.Query().Get("targetId")
	instruction := r.URL.Query().Get("instruction")
	annotationIDs := parseAnnotationIDs(r)

	limit := parseIntParam(r, "limit", 10)
	offset := parseIntParam(r, "offset", 0)

	isDeleted := parseInt8PtrParam(r, "isDeleted")
	isEval := parseInt8PtrParam(r, "isEval")
	source := parseInt8PtrParam(r, "source")
	isReady := parseInt8PtrParam(r, "isReady")

	startTime, endTime := parseTimeParams(r)
	w.Header().Set("Content-Type", "application/json")
	// 1. 处理业务逻辑
	resp, err := annotation.GetAnnotations(
		rtxValues,
		appID,
		targetID,
		instruction,
		annotationIDs,
		limit,
		offset,
		isDeleted,
		isEval,
		source,
		isReady,
		startTime,
		endTime,
	)
	util.CommonProcessAPIResponse(w, r, resp, err, "获取标注内容列表")
}

// deleteAnnotation 软删除标注内容接口
func deleteAnnotation(w http.ResponseWriter, r *http.Request) {
	util.CommonProcessAPI(
		w,
		r,
		&sandbox_api_model.DeleteAnnotationReq{},
		annotation.DeleteAnnotation,
		"软删除标注内容",
	)
}

// deleteAnnotationOperation 软删除标注操作记录接口
func deleteAnnotationOperation(w http.ResponseWriter, r *http.Request) {
	util.CommonProcessAPI(
		w,
		r,
		&sandbox_api_model.DeleteAnnotationOperationReq{},
		annotation.DeleteAnnotationOperation,
		"软删除标注操作记录",
	)
}

// evalAnnotation 标注评估接口
func evalAnnotation(w http.ResponseWriter, r *http.Request) {
	util.CommonProcessAPI(
		w,
		r,
		&sandbox_api_model.EvalAnnotationReq{},
		annotation.EvalAnnotation,
		"标注评估",
	)
}

// updateAnnotationOperation 更新标注操作记录接口
func updateAnnotationOperation(w http.ResponseWriter, r *http.Request) {
	util.CommonProcessAPI(
		w,
		r,
		&sandbox_api_model.UpdateAnnotationOperationReq{},
		annotation.UpdateAnnotationOperation,
		"更新标注操作记录",
	)
}

// updateAnnotation 更新标注内容接口
func updateAnnotation(w http.ResponseWriter, r *http.Request) {
	util.CommonProcessAPI(
		w,
		r,
		&sandbox_api_model.UpdateAnnotationReq{},
		annotation.UpdateAnnotation,
		"更新标注内容",
	)
}

// readyAnnotation 设置标注为 ready 状态接口
func readyAnnotation(w http.ResponseWriter, r *http.Request) {
	util.CommonProcessAPI(
		w,
		r,
		&sandbox_api_model.ReadyAnnotationReq{},
		annotation.ReadyAnnotation,
		"设置标注为 ready 状态",
	)
}
