// Package api 初始化所有接口
package api

import (
	"encoding/json"
	"net/http"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/filter"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/server"
	"github.com/gorilla/mux"

	agent_api "mmfinderdrsandboxagentsvr/api/agent"
	ops_api "mmfinderdrsandboxagentsvr/api/ops"
	sandbox_api "mmfinderdrsandboxagentsvr/api/sandbox"
	sandbox_service "mmfinderdrsandboxagentsvr/service/sandbox"
)

func init() {
	// 拦截器
	filter.Register("HeaderMiddleware", TrpcHeaderMiddleware, nil)
	filter.Register("LogMiddleware", TrpcLogMiddleware, nil)
}

// Init 初始化
func Init(trpcServer *server.Server) {
	// 初始化沙箱
	sandbox_service.InitSandboxOnline()
	// 配置自定义错误处理器
	thttp.DefaultServerCodec.ErrHandler = func(w http.ResponseWriter, r *http.Request, e *errs.Error) {
		ctx := r.Context()
		w.Header().Set("Content-Type", "application/json")
		// tRPC 错误码到 HTTP 状态码的映射
		httpStatus := mapTRPCErrorToHTTP(e.Code)
		w.WriteHeader(httpStatus)
		// 响应体
		resp := map[string]interface{}{
			"code":    e.Code,
			"message": e.Msg,
			"type":    getErrorType(e.Code),
		}
		// 写入 response
		if err := json.NewEncoder(w).Encode(resp); err != nil {
			log.WithContext(
				ctx,
				log.Field{Key: "path", Value: r.URL.Path},
				log.Field{Key: "raw_query", Value: r.URL.RawQuery},
				log.Field{Key: "error", Value: err},
			).Error("写入 response 失败")
		}
	}
	// 创建 mux 路由器
	router := mux.NewRouter()
	// 注册路由
	agent_api.Init(router)
	sandbox_api.Init(router)
	ops_api.Init(router)
	// 将 mux 路由器注册到 tRPC HTTP 服务
	thttp.RegisterNoProtocolServiceMux(trpcServer.Service("trpc.mmfinderdrsandbox.mmfinderdrsandboxagentsvr"), router)
}

// Exit 优雅退出程序
func Exit() {
	// sandbox_service.SandboxOnlineObj.Exit()
}

// getErrorType 获取 tRPC 错误码类型
func getErrorType(trpcCode int32) string {
	switch {
	case trpcCode == 0:
		return "success"
	case trpcCode >= 1 && trpcCode <= 99:
		return "framework_error"
	case trpcCode >= 100 && trpcCode <= 199:
		return "client_error"
	case trpcCode >= 1000:
		return "business_error"
	default:
		return "unknown_error"
	}
}

func mapTRPCErrorToHTTP(trpcCode int32) int {
	switch trpcCode {
	case 0: // 成功
		return http.StatusOK
	case 1, 2, 121, 122: // 编解码错误
		return http.StatusBadRequest
	case 11, 12: // 服务/方法未实现
		return http.StatusNotImplemented
	case 21, 24, 101, 102: // 超时
		return http.StatusRequestTimeout
	case 22, 23, 123, 124: // 限流/过载
		return http.StatusTooManyRequests
	case 31: // 系统错误
		return http.StatusInternalServerError
	case 41: // 鉴权失败
		return http.StatusForbidden
	case 51, 151: // 参数校验失败
		return http.StatusUnprocessableEntity
	case 111, 131, 141: // 连接/路由问题
		return http.StatusBadGateway
	case 161: // 请求取消
		return http.StatusGone
	case 171, 201, 351: // 流式错误
		return http.StatusBadRequest
	case 999: // 未明确错误
		return http.StatusInternalServerError
	default: // 业务错误码（1000+）
		if trpcCode >= 1000 {
			return http.StatusConflict // 499 Conflict 表示业务逻辑错误
		}
		return http.StatusInternalServerError
	}
}
