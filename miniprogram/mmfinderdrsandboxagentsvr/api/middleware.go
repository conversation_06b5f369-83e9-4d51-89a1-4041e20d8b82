package api

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/filter"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/wego/wxg/weenv"
)

// TrpcHeaderMiddleware 给响应增加 header
func TrpcHeaderMiddleware(
	ctx context.Context, req interface{}, next filter.ServerHandleFunc,
) (rsp interface{}, err error) {
	// 1. 直接从 thttp 获取 ResponseWriter， 用于设置 Header
	response := thttp.Response(ctx)
	if response != nil {
		response.Header().Set("x-ip", weenv.GetInnerIp())
	}
	// 2. 继续执行后续逻辑
	return next(ctx, req)
}

// TrpcLogMiddleware 日志中间件
func TrpcLogMiddleware(
	ctx context.Context, req interface{}, next filter.ServerHandleFunc,
) (rsp interface{}, err error) {
	// 1. 从 Header 中取出 x-workflow-run-id
	request := thttp.Head(ctx).Request
	workflowRunID := request.Header.Get("x-workflow-run-id")
	// 2. 从 context 中提取 tRPC 消息元数据
	msg := codec.Message(ctx)
	// 3. 绑定字段到日志上下文
	msg.WithLogger(log.GetDefaultLogger().With(log.Field{
		Key:   "workflow_run_id",
		Value: workflowRunID,
	}))
	// 4. 继续执行后续逻辑
	return next(ctx, req)
}
