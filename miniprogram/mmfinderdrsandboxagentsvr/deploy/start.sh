#!/bin/bash
set -e

# 处理命令行参数
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    --auth_code)
      AUTH_CODE="$2"
      shift 2
      ;;
    *)
      shift
      ;;
  esac
done

# 如果未设置授权码，使用默认值
if [ -z "$AUTH_CODE" ]; then
    echo "未指定授权码"
fi

# 信号处理
cleanup() {
    echo "正在关闭服务..."
    if [ -n "$PROXY_PID" ]; then
        kill $PROXY_PID
    fi
    if [ -n "$WECHAT_PID" ]; then
        kill $WECHAT_PID
    fi
    if [ -n "$XVFB_PID" ]; then
        kill $XVFB_PID
    fi
    exit 0
}

trap cleanup SIGTERM SIGINT

# 设置系统参数
sudo sysctl -w vm.max_map_count=262144 || true
sudo sysctl -w fs.file-max=65535 || true
ulimit -n 65535 || true

# 设置core dump配置，实际运用中无权限运行下面这两行
# sudo bash -c 'ulimit -c unlimited'
# sudo bash -c 'echo /home/<USER>/workspace/dmp/core.%e.%p.%t > /proc/sys/kernel/core_pattern'

# 启动Xvfb虚拟显示服务器
echo "启动Xvfb虚拟显示服务器..."
Xvfb :99 -screen 0 1280x1024x24 -ac &
XVFB_PID=$!
export DISPLAY=:99

# 等待Xvfb启动
sleep 2

# 启动D-Bus会话总线
echo "启动D-Bus会话总线..."
if [ -z "$DBUS_SESSION_BUS_ADDRESS" ]; then
    eval $(dbus-launch --sh-syntax)
fi

# 准备WMPF目录
mkdir -p /home/<USER>/workspace/WMPF
cd /home/<USER>/workspace

# 根据系统架构查找并解压适合的WeChat压缩包
ARCH=$(uname -m)
if [ "$ARCH" = "x86_64" ]; then
    echo "检测到x86_64架构，查找x64版本"
    
    # 查找RadiumWMPF包
    X64_PACKAGE=$(ls RadiumWMPF_x64*.tar.gz 2>/dev/null || echo "")
    
    if [ -z "$X64_PACKAGE" ]; then
        echo "未找到适用于x64架构的RadiumWMPF包！"
        exit 1
    fi
    
    echo "使用压缩包: $X64_PACKAGE"
    tar -xzf "$X64_PACKAGE" -C WMPF
elif [ "$ARCH" = "arm64" ] || [ "$ARCH" = "aarch64" ]; then
    echo "检测到arm64架构，查找arm64版本"
    
    # 查找RadiumWMPF包
    ARM64_PACKAGE=$(ls RadiumWMPF_arm64*.tar.gz 2>/dev/null || echo "")
    
    if [ -z "$ARM64_PACKAGE" ]; then
        echo "未找到适用于arm64架构的RadiumWMPF包！"
        exit 1
    fi
    
    echo "使用压缩包: $ARM64_PACKAGE"
    tar -xzf "$ARM64_PACKAGE" -C WMPF
else
    echo "不支持的架构: $ARCH"
    exit 1
fi

# 确保WMPF可执行
chmod +x /home/<USER>/workspace/WMPF/host/wmpf_agent
# 配置ilink长短链接的服务地址
echo "{\"debug_ip\": \"***********\", \"short_debug_ip\": \"**************\"}" > /home/<USER>/workspace/WMPF/host/wmpf_agent.config

# 启动proxy.jar在后台
java -jar /home/<USER>/workspace/proxy.jar &
PROXY_PID=$!

# 等待proxy.jar启动完成
echo "等待proxy.jar启动，休眠30秒..."
sleep 30

# 检查proxy.jar是否仍在运行
if ! kill -0 $PROXY_PID 2>/dev/null; then
    echo "proxy.jar启动失败！"
    exit 1
fi

# 进入WMPF目录并启动WMPF应用
cd /home/<USER>/workspace/WMPF
echo "使用授权码: $AUTH_CODE 启动WMPF应用"

# 创建必要的目录结构
# sudo rm -rf /home/<USER>/.xwechat
# sudo mkdir -p /home/<USER>/.xwechat/crashinfo/attachments
# sudo mkdir -p /home/<USER>/.xwechat/logs
# sudo mkdir -p /home/<USER>/.xwechat/data
# sudo chown -R ubuntu:ubuntu /home/<USER>/.xwechat
# sudo chmod -R 777 /home/<USER>/.xwechat

# 确保wmpf_agent有执行权限
sudo chmod +x ./host/wmpf_agent

# 使用更多参数运行wmpf_agent
echo "启动WMPF应用..."
if [ -z "$AUTH_CODE" ];
then
    ./host/wmpf_agent &
else
    ./host/wmpf_agent --auth-code="$AUTH_CODE" &
fi
WMPF_PID=$!

echo "WMPF应用和代理服务已启动！"
echo "代理服务运行在端口8081上"

# 等待任意子进程退出
wait -n

# 如果有进程退出，则清理并退出
cleanup 