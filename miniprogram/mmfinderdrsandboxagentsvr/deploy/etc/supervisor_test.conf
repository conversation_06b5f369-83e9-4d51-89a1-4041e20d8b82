[unix_http_server]
file=/tmp/mmfinderdrsandboxagentsvr_mmfinderdrsandboxagentsvr.supervior.sock
chmod=0760
chown=root:users

[supervisord]
nodaemon=true
logfile=/tmp/mmfinderdrsandboxagentsvr_mmfinderdrsandboxagentsvr_supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info

[supervisorctl]
serverurl = unix:///tmp/mmfinderdrsandboxagentsvr_mmfinderdrsandboxagentsvr.supervior.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

; use nodaemon to start proc
[program:mmfinderdrsandboxagentsvr]
command=sh -c "killall -9 mmfinderdrsandboxagentsvr;/home/<USER>/mmfinderdrsandboxagentsvr/sbin/mmfinderdrsandboxagentsvr --conf /home/<USER>/mmfinderdrsandboxagentsvr/etc/trpc_go.yaml"
autostart=true
autorestart=true
stopsignal=KILL
stopasgroup=true
killasgroup=true