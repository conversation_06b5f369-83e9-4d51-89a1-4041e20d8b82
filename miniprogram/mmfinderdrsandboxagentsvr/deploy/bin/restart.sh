
#!/usr/bin/sh

# killall

supervisorTestPath="/home/<USER>/mmfinderdrsandboxagentsvr/etc/supervisor_test.conf"

ps aux | grep "/usr/bin/supervisord" | grep -v grep

# use supervisord
if [ $? -eq 0 ]
then
    supervisorctl -c ${supervisorTestPath} restart all

elif [ -f ${supervisorTestPath} ]
then
    nohup /usr/bin/supervisord -c ${supervisorTestPath} >/home/<USER>/data/mmfinderdrsandboxagentsvr/supervisor_test.log 2>&1 &

# not use supervisord
else
    killall -9 mmfinderdrsandboxagentsvr

    # command must be runned as daemon
    /home/<USER>/mmfinderdrsandboxagentsvr/sbin/mmfinderdrsandboxagentsvr /home/<USER>/mmfinderdrsandboxagentsvr/etc/conf.toml -d
fi