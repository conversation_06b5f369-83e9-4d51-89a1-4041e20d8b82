"""
大模型调用相关
"""

import asyncio
import json
import os
import traceback
from copy import deepcopy
from typing import Any

import httpx
from func_timeout.exceptions import FunctionTimedOut
from httpx import Limits
from openai import AsyncOpenAI, AsyncStream, OpenAI, Stream
from openai.types.chat import ChatCompletionChunk
from opentelemetry.instrumentation.openai.shared import chat_wrappers
from wxms.logger import logger, process_image_item
from wxms.service.clickhouse_service import Action, clickhouse_service
from wxms.util.common_util import common_util
from wxms.util.context_util import context_util
from wxms.util.httpx_util import httpx_util
from wxms.util.time_util import time_util


class LlmModel:
    """
    大模型调用相关，由调用方自己初始化一个对象
    """

    def __init__(
        self,
        base_url: str,
        api_key: str,
        default_headers: dict[str, str] | None = None,
        proxy: str | None = None,
    ):
        # 加载默认的 ssl context，因为 ssl 模块的证书加载和验证为同步阻塞的
        httpx_kwargs = {
            "verify": httpx_util.httpx_ssl_context,
            "limits": Limits(max_connections=100, max_keepalive_connections=20),
            "proxy": proxy,
        }
        # 实例化 client base
        openai_client_kwargs = {
            "base_url": base_url,
            "api_key": api_key,
            "timeout": httpx.Timeout(timeout=180.0, connect=5.0),
            "max_retries": 3,
            "default_headers": default_headers,
        }
        self.async_client = AsyncOpenAI(
            **openai_client_kwargs,
            http_client=httpx.AsyncClient(**httpx_kwargs),
        )
        self.client = OpenAI(
            **openai_client_kwargs,
            http_client=httpx.Client(**httpx_kwargs),
        )

    def chat_completion(
        self,
        model: str,
        messages: list[dict],
        stream: bool = False,
        first_token_timeout_for_stream: int = 60,
        **kwargs,
    ) -> tuple[str, str]:
        """
        段式调用大模型并返回结果，允许底层实现为流式调用，则可以有第一个 token 超时的功能

        :param model: 模型名
        :param messages: 上下文
        :param stream: 底层实现是否为流式调用
        :param first_token_timeout_for_stream: 当为流式调用时，如果第一个 token 超时则抛出异常
        :return: 模型调用的结果
            第一个元素：completion.choices[0].message.content 或流式的拼接结果
            第二个元素：预留了一个 reasoning_content 的位置，目前还未开发，都是空字符串
        """
        request_dict = {"model": model, "messages": messages, "stream": stream}
        request_dict.update(kwargs)
        request_dict_for_report = deepcopy(request_dict)
        messages_copy = deepcopy(messages)
        for msg in messages_copy:
            content = msg.get("content")
            if isinstance(content, list):
                content = [
                    (
                        process_image_item(item)
                        if chat_wrappers._is_base64_image(item)  # pylint: disable=W0212
                        else item
                    )
                    for item in content
                ]
            msg["content"] = content
        request_dict_for_report["messages"] = messages_copy
        content = ""
        try:
            if stream:
                start_time = time_util.get_millisecond_timestamp_of_current_time()
                completion, content = time_util.execute_function_with_timeout(
                    first_token_timeout_for_stream,
                    self.__get_completion_and_first_none_empty_content,
                    **request_dict,
                )
                middle_time = time_util.get_millisecond_timestamp_of_current_time()
                logger.info(
                    "get first token in stream",
                    extra={
                        "customized_data_info": {
                            "content": content,
                            "middle_time_cost": middle_time - start_time,
                            "end_time_cost": time_util.get_millisecond_timestamp_of_current_time()
                            - start_time,
                        }
                    },
                )
                for chunk in completion:
                    chunk_dict = chunk.model_dump()
                    content += self.__get_delta_content_from_chunk_dict(chunk_dict)
            else:
                started_at = time_util.get_millisecond_timestamp_of_current_time()
                completion = self.client.chat.completions.create(**request_dict)
                content = completion.choices[0].message.content
                # 上报数据给 clickhouse，这个是 chat_completion 在非流式情况下特有的逻辑
                clickhouse_service.add_action(
                    Action(
                        started_at=started_at,
                        ended_at=time_util.get_millisecond_timestamp_of_current_time(),
                        session_id=context_util.get_trace_id(),
                        sub_session_id=context_util.get_data_from_context(
                            "sandbox_sub_session_id"
                        ),
                        step_id=context_util.get_data_from_context("sandbox_step_id"),
                        action_id=completion.id,
                        parent_action_id=context_util.get_data_from_context(
                            "sandbox_action_id"
                        ),
                        action_type="openai",
                        parameter=json.dumps(request_dict_for_report),
                        result=completion.model_dump_json(),
                    )
                )
        except (FunctionTimedOut, Exception) as e:  # pylint: disable=W0718
            logger.error(
                "chat_completion failed",
                extra={
                    "customized_data_info": {
                        "request_dict": json.dumps(
                            request_dict_for_report, ensure_ascii=False
                        ),
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
            if not isinstance(e, FunctionTimedOut):
                raise e
        if not content:
            logger.warning(
                "chat_completion content empty",
                extra={
                    "customized_data_info": {
                        "request_dict": json.dumps(
                            request_dict_for_report, ensure_ascii=False
                        ),
                    }
                },
            )
        return content, ""

    async def chat_completion_async(
        self,
        model: str,
        messages: list[dict],
        stream: bool = False,
        first_token_timeout_for_stream: int = 60,
        **kwargs,
    ) -> tuple[str, str]:
        """
        段式调用大模型并返回结果，允许底层实现为流式调用，则可以有第一个 token 超时的功能

        :param model: 模型名
        :param messages: 上下文
        :param stream: 底层实现是否为流式调用
        :param first_token_timeout_for_stream: 当为流式调用时，如果第一个 token 超时则抛出异常
        :return: 模型调用的结果
            第一个元素：completion.choices[0].message.content 或流式的拼接结果
            第二个元素：预留了一个 reasoning_content 的位置，目前还未开发，都是空字符串
        """
        request_dict = {"model": model, "messages": messages, "stream": stream}
        request_dict.update(kwargs)
        request_dict_for_report = deepcopy(request_dict)
        messages_copy = deepcopy(messages)
        for msg in messages_copy:
            content = msg.get("content")
            if isinstance(content, list):
                content = [
                    (
                        process_image_item(item)
                        if chat_wrappers._is_base64_image(item)  # pylint: disable=W0212
                        else item
                    )
                    for item in content
                ]
            msg["content"] = content
        request_dict_for_report["messages"] = messages_copy
        content = ""
        try:
            if stream:
                start_time = time_util.get_millisecond_timestamp_of_current_time()
                completion, content = (
                    await time_util.execute_function_with_timeout_async(
                        first_token_timeout_for_stream,
                        self.__get_completion_and_first_none_empty_content_async,
                        **request_dict,
                    )
                )
                middle_time = time_util.get_millisecond_timestamp_of_current_time()
                logger.info(
                    "get first token in stream",
                    extra={
                        "customized_data_info": {
                            "content": content,
                            "middle_time_cost": middle_time - start_time,
                            "end_time_cost": time_util.get_millisecond_timestamp_of_current_time()
                            - start_time,
                        }
                    },
                )
                async for chunk in completion:
                    chunk_dict = chunk.model_dump()
                    content += self.__get_delta_content_from_chunk_dict(chunk_dict)
            else:
                completion = await self.async_client.chat.completions.create(
                    **request_dict
                )
                content = completion.choices[0].message.content
        except (asyncio.TimeoutError, Exception) as e:  # pylint: disable=W0718
            logger.error(
                "chat_completion_async failed",
                extra={
                    "customized_data_info": {
                        "request_dict": json.dumps(
                            request_dict_for_report, ensure_ascii=False
                        ),
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
            if not isinstance(e, asyncio.TimeoutError):
                raise e
        if not content:
            logger.warning(
                "chat_completion_async content empty",
                extra={
                    "customized_data_info": {
                        "request_dict": json.dumps(
                            request_dict_for_report, ensure_ascii=False
                        ),
                    }
                },
            )
        return content, ""

    def __get_completion_and_first_none_empty_content(
        self, **request_dict
    ) -> tuple[Stream[ChatCompletionChunk], str]:
        completion = self.client.chat.completions.create(**request_dict)
        completion: Stream[ChatCompletionChunk] = completion
        res = ""
        while not res:
            chunk = next(completion)
            chunk_dict = chunk.model_dump()
            res = self.__get_delta_content_from_chunk_dict(chunk_dict)
        return completion, res

    async def __get_completion_and_first_none_empty_content_async(
        self, **request_dict
    ) -> tuple[AsyncStream[ChatCompletionChunk], str]:
        completion: AsyncStream[ChatCompletionChunk] = (
            await self.async_client.chat.completions.create(  # pylint: disable=E1125
                **request_dict
            )
        )
        res = ""
        while not res:
            chunk = await anext(completion)
            chunk_dict = chunk.model_dump()
            res = self.__get_delta_content_from_chunk_dict(chunk_dict)
        return completion, res

    def __get_delta_content_from_chunk_dict(self, chunk_dict: dict[str, Any]) -> str:
        res = ""
        try:
            res = chunk_dict["choices"][0]["delta"]["content"]
        except Exception:  # pylint: disable=W0718
            pass
        if not isinstance(res, str):
            res = ""
        return res


def __test():
    llm_model = LlmModel(
        base_url=os.getenv(
            "TEST_OPENAI_BASE_URL",
            "http://drhttpsvr.polaris:8000/v1/llm-luban-waitmodel_7B_606_Qwen2.5-VL-7B-wait-0702_ck1100_export-0702-19",  # pylint: disable=C0301
        ),
        api_key=os.getenv("TEST_OPENAI_API_KEY", "EMPTY"),
        proxy=os.getenv("TEST_OPENAI_PROXY", None),
    )
    model = os.getenv(
        "TEST_OPENAI_MODEL",
        "llm-luban-waitmodel_7B_606_Qwen2.5-VL-7B-wait-0702_ck1100_export-0702-19",
    )
    image_list = [
        "/home/<USER>/workspace/cat.jpg",
        # "/Users/<USER>/Desktop/cat.jpg",
    ]
    prompt = "please tell me what's in the next picture"
    temp_messages: list[dict[str, Any]] = [
        # {
        #     "role": "system",
        #     "content": "The following is a conversation with an AI assistant. The assistant is helpful, creative, clever, and very friendly.",  # pylint: disable=C0301
        # },
        {
            "role": "user",
            "content": prompt,
        },
    ]
    for image in image_list:
        temp_messages.append(
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{common_util.encode_image(image)}"
                        },
                    },
                ],
            }
        )
    logger.info(
        llm_model.chat_completion(
            model=model,
            messages=temp_messages,
            # stream=True,
            # first_token_timeout_for_stream=1,
        )
    )
    loop = asyncio.get_event_loop()
    logger.info(
        loop.run_until_complete(
            llm_model.chat_completion_async(
                model=model,
                messages=temp_messages,
                # stream=True,
                # first_token_timeout_for_stream=1,
            )
        )
    )


if __name__ == "__main__":
    __test()
