"""
cos 工具类
"""

from qcloud_cos import CosConfig, CosS3Client


class CosClient:
    """
    cos 工具类
    """

    def __init__(
        self,
        region: str,
        service_domain: str,
        endpoint: str,
        bucket: str,
        secret_id: str,
        secret_key: str,
    ):
        """
        初始化

        :param region: 区域名称
        :param service_domain: 服务域名
        :param endpoint: 桶域名
        :param bucket: 存储桶名称
        :param secret_id: 访问密钥 id
        :param secret_key: 访问密钥 key
        """
        self.region = region
        self.service_domain = service_domain
        self.endpoint = endpoint
        self.bucket = bucket
        self.secret_id = secret_id
        self.secret_key = secret_key

        self.client = CosS3Client(
            CosConfig(
                Region=self.region,
                SecretId=self.secret_id,
                SecretKey=self.secret_key,
                Scheme="http",
                Endpoint=self.endpoint,
                ServiceDomain=self.service_domain,
            )
        )

    def upload_object(self, object_key: str, file: str, content_type: str) -> str:
        """
        上传本地文件到 cos

        :param object_key: 对象存储的 key
        :param file: 本地文件的路径
        :param content_type: 文件的 mime type
        :return: object 的 download url
        """
        self.client.put_object_from_local_file(
            Bucket=self.bucket,
            LocalFilePath=file,
            Key=object_key,
            ContentType=content_type,
        )
        return self.client.get_object_url(
            Bucket=self.bucket,
            Key=object_key,
        )
