"""
redis 链接池
"""

import asyncio
from typing import List

import redis
import redis.asyncio as aioredis


class RedisPool(object):
    """
    redis 同步链接池
    """

    def __init__(
        self,
        host: str,
        port: int = 6379,
        password: str = "",
        max_connections: int | None = None,
        init_dbs: List[int] | None = None,
    ):
        self.redis_pool_dict = {}
        self.host = host
        self.port = port
        self.password = password
        # 如果设置了且并发达到了最大数量会抛出异常
        self.max_connections = max_connections
        self.init_dbs = init_dbs
        if self.init_dbs is not None:
            for db in self.init_dbs:
                self.__get_or_set_redis_connection_pool(db)

    def get_redis(self, db: int = 0) -> redis.Redis:
        """
        获取指定 db 的 redis client，redis client 由链接池生成

        :param db: redis 的数据库编号
        :return: redis client
        """
        pool = self.__get_or_set_redis_connection_pool(db)
        return redis.Redis(connection_pool=pool)

    def __get_or_set_redis_connection_pool(self, db: int) -> redis.ConnectionPool:
        pool = self.redis_pool_dict.get(db)
        if pool:
            return pool
        param_dict = {
            "host": self.host,
            "port": self.port,
            "db": db,
            "max_connections": self.max_connections,
        }
        if self.password:
            param_dict["password"] = self.password
        return self.redis_pool_dict.setdefault(db, redis.ConnectionPool(**param_dict))


class AsyncRedisPool(object):
    """
    redis 异步链接池
    """

    def __init__(
        self,
        host: str,
        port: int = 6379,
        password: str = "",
        max_connections: int = 50,
        health_check_interval: int = 20,
        timeout: int = 20,
        init_dbs: List[int] | None = None,
    ):
        self.redis_pool_dict = {}
        self.host = host
        self.port = port
        self.password = password
        self.max_connections = max_connections
        self.health_check_interval = health_check_interval
        self.timeout = timeout
        if init_dbs is not None:
            for db in init_dbs:
                self.__get_or_set_redis_connection_pool(db)

    async def get_redis(self, db: int) -> aioredis.Redis:
        """
        获取指定 db 的 redis client，redis client 由链接池生成

        :param db: redis 的数据库编号
        :return: redis client
        """
        try:
            pool = self.__get_or_set_redis_connection_pool(db)
            redis_conn = aioredis.Redis(connection_pool=pool)
            await redis_conn.ping()  # 尝试 ping 一下看下链接是否存活
            return redis_conn
        except asyncio.TimeoutError as e:
            self.redis_pool_dict.pop(db, None)
            raise e
        except Exception as e:
            self.redis_pool_dict.pop(db, None)
            raise e

    def __get_or_set_redis_connection_pool(
        self, db: int
    ) -> aioredis.BlockingConnectionPool:
        pool = self.redis_pool_dict.get(db)
        if pool:
            return pool
        if self.password:
            url = f"redis://default:{self.password}@{self.host}:{self.port}/{db}"
        else:
            url = f"redis://{self.host}:{self.port}/{db}"
        pool = aioredis.BlockingConnectionPool.from_url(
            url,
            timeout=self.timeout,
            max_connections=self.max_connections,
            health_check_interval=self.health_check_interval,
        )
        return self.redis_pool_dict.setdefault(db, pool)
