
window.__WX_WM_SANDBOX_VERSION__ = '0.3.25';
window.__WX_WM_SANDBOX_BUILD_TIME__ = '2025/7/3 16:58:11';
"use strict";(()=>{var M={silent:Number.NEGATIVE_INFINITY,fatal:0,error:0,warn:1,log:2,info:3,success:3,fail:3,ready:3,start:3,box:3,debug:4,trace:5,verbose:Number.POSITIVE_INFINITY},se={silent:{level:-1},fatal:{level:M.fatal},error:{level:M.error},warn:{level:M.warn},log:{level:M.log},info:{level:M.info},success:{level:M.success},fail:{level:M.fail},ready:{level:M.info},start:{level:M.info},box:{level:M.info},debug:{level:M.debug},trace:{level:M.trace},verbose:{level:M.verbose}};function oe(t){if(t===null||typeof t!="object")return!1;let e=Object.getPrototypeOf(t);return e!==null&&e!==Object.prototype&&Object.getPrototypeOf(e)!==null||Symbol.iterator in t?!1:Symbol.toStringTag in t?Object.prototype.toString.call(t)==="[object Module]":!0}function ae(t,e,o=".",i){if(!oe(e))return ae(t,{},o,i);let r=Object.assign({},e);for(let u in t){if(u==="__proto__"||u==="constructor")continue;let f=t[u];f!=null&&(i&&i(r,u,f,o)||(Array.isArray(f)&&Array.isArray(r[u])?r[u]=[...f,...r[u]]:oe(f)&&oe(r[u])?r[u]=ae(f,r[u],(o?`${o}.`:"")+u.toString(),i):r[u]=f))}return r}function nt(t){return(...e)=>e.reduce((o,i)=>ae(o,i,"",t),{})}var ot=nt();function rt(t){return Object.prototype.toString.call(t)==="[object Object]"}function it(t){return!(!rt(t)||!t.message&&!t.args||t.stack)}var re=!1,Ie=[],x=class t{options;_lastLog;_mockFn;constructor(e={}){let o=e.types||se;this.options=ot({...e,defaults:{...e.defaults},level:ie(e.level,o),reporters:[...e.reporters||[]]},{types:se,throttle:1e3,throttleMin:5,formatOptions:{date:!0,colors:!1,compact:!0}});for(let i in o){let r={type:i,...this.options.defaults,...o[i]};this[i]=this._wrapLogFn(r),this[i].raw=this._wrapLogFn(r,!0)}this.options.mockFn&&this.mockTypes(),this._lastLog={}}get level(){return this.options.level}set level(e){this.options.level=ie(e,this.options.types,this.options.level)}prompt(e,o){if(!this.options.prompt)throw new Error("prompt is not supported!");return this.options.prompt(e,o)}create(e){let o=new t({...this.options,...e});return this._mockFn&&o.mockTypes(this._mockFn),o}withDefaults(e){return this.create({...this.options,defaults:{...this.options.defaults,...e}})}withTag(e){return this.withDefaults({tag:this.options.defaults.tag?this.options.defaults.tag+":"+e:e})}addReporter(e){return this.options.reporters.push(e),this}removeReporter(e){if(e){let o=this.options.reporters.indexOf(e);if(o!==-1)return this.options.reporters.splice(o,1)}else this.options.reporters.splice(0);return this}setReporters(e){return this.options.reporters=Array.isArray(e)?e:[e],this}wrapAll(){this.wrapConsole(),this.wrapStd()}restoreAll(){this.restoreConsole(),this.restoreStd()}wrapConsole(){for(let e in this.options.types)console["__"+e]||(console["__"+e]=console[e]),console[e]=this[e].raw}restoreConsole(){for(let e in this.options.types)console["__"+e]&&(console[e]=console["__"+e],delete console["__"+e])}wrapStd(){this._wrapStream(this.options.stdout,"log"),this._wrapStream(this.options.stderr,"log")}_wrapStream(e,o){e&&(e.__write||(e.__write=e.write),e.write=i=>{this[o].raw(String(i).trim())})}restoreStd(){this._restoreStream(this.options.stdout),this._restoreStream(this.options.stderr)}_restoreStream(e){e&&e.__write&&(e.write=e.__write,delete e.__write)}pauseLogs(){re=!0}resumeLogs(){re=!1;let e=Ie.splice(0);for(let o of e)o[0]._logFn(o[1],o[2])}mockTypes(e){let o=e||this.options.mockFn;if(this._mockFn=o,typeof o=="function")for(let i in this.options.types)this[i]=o(i,this.options.types[i])||this[i],this[i].raw=this[i]}_wrapLogFn(e,o){return(...i)=>{if(re){Ie.push([this,e,i,o]);return}return this._logFn(e,i,o)}}_logFn(e,o,i){if((e.level||0)>this.level)return!1;let r={date:new Date,args:[],...e,level:ie(e.level,this.options.types)};!i&&o.length===1&&it(o[0])?Object.assign(r,o[0]):r.args=[...o],r.message&&(r.args.unshift(r.message),delete r.message),r.additional&&(Array.isArray(r.additional)||(r.additional=r.additional.split(`
`)),r.args.push(`
`+r.additional.join(`
`)),delete r.additional),r.type=typeof r.type=="string"?r.type.toLowerCase():"log",r.tag=typeof r.tag=="string"?r.tag:"";let u=(l=!1)=>{let w=(this._lastLog.count||0)-this.options.throttleMin;if(this._lastLog.object&&w>0){let b=[...this._lastLog.object.args];w>1&&b.push(`(repeated ${w} times)`),this._log({...this._lastLog.object,args:b}),this._lastLog.count=1}l&&(this._lastLog.object=r,this._log(r))};clearTimeout(this._lastLog.timeout);let f=this._lastLog.time&&r.date?r.date.getTime()-this._lastLog.time.getTime():0;if(this._lastLog.time=r.date,f<this.options.throttle)try{let l=JSON.stringify([r.type,r.tag,r.args]),w=this._lastLog.serialized===l;if(this._lastLog.serialized=l,w&&(this._lastLog.count=(this._lastLog.count||0)+1,this._lastLog.count>this.options.throttleMin)){this._lastLog.timeout=setTimeout(u,this.options.throttle);return}}catch{}u(!0)}_log(e){for(let o of this.options.reporters)o.log(e,{options:this.options})}};function ie(t,e={},o=3){return t===void 0?o:typeof t=="number"?t:e[t]&&e[t].level!==void 0?e[t].level:o}x.prototype.add=x.prototype.addReporter;x.prototype.remove=x.prototype.removeReporter;x.prototype.clear=x.prototype.removeReporter;x.prototype.withScope=x.prototype.withTag;x.prototype.mock=x.prototype.mockTypes;x.prototype.pause=x.prototype.pauseLogs;x.prototype.resume=x.prototype.resumeLogs;function Ne(t={}){return new x(t)}var ce=class{options;defaultColor;levelColorMap;typeColorMap;constructor(e){this.options={...e},this.defaultColor="#7f8c8d",this.levelColorMap={0:"#c0392b",1:"#f39c12",3:"#00BCD4"},this.typeColorMap={success:"#2ecc71"}}_getLogFn(e){return e<1?console.__error||console.error:e===1?console.__warn||console.warn:console.__log||console.log}log(e){let o=this._getLogFn(e.level),i=e.type==="log"?"":e.type,r=e.tag||"",f=`
      background: ${this.typeColorMap[e.type]||this.levelColorMap[e.level]||this.defaultColor};
      border-radius: 0.5em;
      color: white;
      font-weight: bold;
      padding: 2px 0.5em;
    `,l=`%c${[r,i].filter(Boolean).join(":")}`;typeof e.args[0]=="string"?o(`${l}%c ${e.args[0]}`,f,"",...e.args.slice(1)):o(l,f,...e.args)}};function st(t={}){return Ne({reporters:t.reporters||[new ce({})],prompt(o,i={}){return i.type==="confirm"?Promise.resolve(confirm(o)):Promise.resolve(prompt(o))},...t})}var X=st();function Ce(){typeof window>"u"||(window.isPdfViewer=!!document?.body?.querySelector('body > embed[type="application/pdf"][width="100%"]'),window.isPdfViewer||(()=>{if(window._eventListenerTrackerInitialized)return;window._eventListenerTrackerInitialized=!0;let t=EventTarget.prototype.addEventListener,e=new WeakMap;EventTarget.prototype.addEventListener=function(o,i,r){if(typeof i=="function"){let u=e.get(this);u||(u=[],e.set(this,u)),u.push({type:o,listener:i,listenerPreview:i.toString().slice(0,100),options:r})}return r=typeof r=="object"?r:{capture:!!r},t.call(this,o,i,r)},window.getEventListenersForNode=o=>(e.get(o)||[]).map(({type:r,listenerPreview:u,options:f})=>({type:r,listenerPreview:u,options:f}))})())}var Le=new Set(["pointer","move","text","grab","grabbing","cell","copy","alias","all-scroll","col-resize","context-menu","crosshair","e-resize","ew-resize","help","n-resize","ne-resize","nesw-resize","ns-resize","nw-resize","nwse-resize","row-resize","s-resize","se-resize","sw-resize","vertical-text","w-resize","zoom-in","zoom-out"]),Se=new Set(["not-allowed","no-drop","wait","progress","initial","inherit"]),z=new Set(["a","button","input","select","textarea","details","summary","label","option","optgroup","fieldset","legend","wx-button","wx-input"]),Re=new Set(["disabled","readonly"]);function ke(t){if(!t||t.nodeType!==Node.ELEMENT_NODE)return!1;let e=t.tagName.toLowerCase();return z.has(e)?!0:t.hasAttribute("onclick")||t.hasAttribute("role")||t.hasAttribute("tabindex")||t.hasAttribute("aria-")||t.hasAttribute("data-action")||t.getAttribute("contenteditable")==="true"}var le=new Set(["button","menuitemradio","menuitemcheckbox","radio","checkbox","tab","switch","slider","spinbutton","combobox","searchbox","textbox","option","scrollbar"]);var ue=(typeof window<"u"?window.__WX_MER__?.consoleLog:void 0)||console.log,at=(typeof window<"u"?window.__WX_MER_DEBUG__:void 0)??!1;at&&ct("\u{1F41B} [DEBUG MODE]",window.__WX_MER_DEBUG__);function ct(...t){ue(...t)}var T={debug:(...t)=>{(window.__WX_MER_DEBUG__??0)>=2&&ue("[MER] [DEBUG]",...t)},log:(...t)=>{(window.__WX_MER_DEBUG__??0)>=1&&ue("[MER]",...t)}},Y=typeof window<"u"?window.navigator:void 0,Nt=/miniProgram/i.test(typeof Y<"u"?Y.userAgent:""),G=!1;typeof document>"u"||document.addEventListener("WeixinJSBridgeReady",()=>{G=window.__wxjs_environment==="miniprogram"});function De(t){return t=(t||Y?.userAgent||"").toLowerCase(),/android|adr/i.test(t)}function Pe(t){return t=(t||Y?.userAgent||"").toLowerCase(),/iphone|ipad|ipod/i.test(t)}var pe={isAndroid:De(),isIOS:Pe(),desc:`${De()?"Android":Pe()?"iOS":"Unknown"} Mini Program`};function k(t,e=!0){let o=[],i=t;for(;i&&i.nodeType===Node.ELEMENT_NODE&&!(e&&(i.parentNode instanceof ShadowRoot||i.parentNode instanceof HTMLIFrameElement));){let r=0,u=i.previousSibling;for(;u;)u.nodeType===Node.ELEMENT_NODE&&u.nodeName===i.nodeName&&r++,u=u.previousSibling;let f=i.nodeName.toLowerCase(),l=r>0?`[${r+1}]`:"";o.unshift(`${f}${l}`),i=i.parentNode}return o.join("/")}Ce();var J={currentIframe:null},U="wx-mer-highlight-container";function H(t={doHighlightElements:!0,focusHighlightIndex:-1,viewportExpansion:0,debugMode:!1,recordRects:!1,skipIframe:!1,recordAll:!1,includeOffscreenElements:!1}){let{doHighlightElements:e,focusHighlightIndex:o=-1,viewportExpansion:i=0,debugMode:r}=t,u=0,f=document.getElementById(U);f&&f.remove();let l={nodeProcessing:[],treeTraversal:[],highlighting:[],current:null},w=window.innerWidth/2,b=window.innerHeight/2,E=document.elementFromPoint(w,b);for(;E&&!["body","iframe"].includes(E?.tagName.toLowerCase());)E=E.parentElement;let N=E;J.currentIframe=N;function ge(n){l[n]=l[n]||[],l[n].push(performance.now())}function V(n){let s=l[n].pop();return performance.now()-s}let c=r?{buildDomTreeCalls:0,timings:{buildDomTree:0,highlightElement:0,isInteractiveElement:0,isElementVisible:0,isTopElement:0,isInExpandedViewport:0,isTextNodeVisible:0,getEffectiveScroll:0},cacheMetrics:{boundingRectCacheHits:0,boundingRectCacheMisses:0,computedStyleCacheHits:0,computedStyleCacheMisses:0,getBoundingClientRectTime:0,getComputedStyleTime:0,boundingRectHitRate:0,computedStyleHitRate:0,overallHitRate:0,clientRectsCacheHits:0,clientRectsCacheMisses:0},nodeMetrics:{totalNodes:0,processedNodes:0,skippedNodes:0},buildDomTreeBreakdown:{totalTime:0,totalSelfTime:0,buildDomTreeCalls:0,domOperations:{getBoundingClientRect:0,getComputedStyle:0},domOperationCounts:{getBoundingClientRect:0,getComputedStyle:0}}}:null;function bt(n){return r?function(...s){let p=performance.now(),a=n.apply(this,s),v=performance.now()-p;return a}:n}function Q(n,s){if(!r)return n();let p=performance.now(),a=n(),v=performance.now()-p;return c&&s in c.buildDomTreeBreakdown.domOperations&&(c.buildDomTreeBreakdown.domOperations[s]+=v,c.buildDomTreeBreakdown.domOperationCounts[s]++),a}let I={boundingRects:new WeakMap,clientRects:new WeakMap,computedStyles:new WeakMap,clearCache:()=>{I.boundingRects=new WeakMap,I.clientRects=new WeakMap,I.computedStyles=new WeakMap}};function F(n){if(!n)return null;if(I.boundingRects.has(n))return r&&c&&c.cacheMetrics.boundingRectCacheHits++,I.boundingRects.get(n);r&&c&&c.cacheMetrics.boundingRectCacheMisses++;let s;if(r){let p=performance.now();s=n.getBoundingClientRect();let a=performance.now()-p;c&&(c.buildDomTreeBreakdown.domOperations.getBoundingClientRect+=a,c.buildDomTreeBreakdown.domOperationCounts.getBoundingClientRect++)}else s=n.getBoundingClientRect();return s&&I.boundingRects.set(n,s),s}function Z(n){if(!n)return null;if(I.computedStyles.has(n))return r&&c&&c.cacheMetrics.computedStyleCacheHits++,I.computedStyles.get(n);r&&c&&c.cacheMetrics.computedStyleCacheMisses++;let s;if(r){let p=performance.now();s=window.getComputedStyle(n);let a=performance.now()-p;c&&(c.buildDomTreeBreakdown.domOperations.getComputedStyle+=a,c.buildDomTreeBreakdown.domOperationCounts.getComputedStyle++)}else s=window.getComputedStyle(n);return s&&I.computedStyles.set(n,s),s}function qe(n){if(!n)return null;if(I.clientRects.has(n))return r&&c&&c.cacheMetrics.clientRectsCacheHits++,I.clientRects.get(n);r&&c&&c.cacheMetrics.clientRectsCacheMisses++;let s=Array.from(n.getClientRects());return s&&I.clientRects.set(n,s),s}let A={},ee={current:0};function Ee(n,s,p=null){if(!n)return s;try{let a=document.getElementById(U);a||(a=document.createElement("div"),a.id=U,a.style.position="fixed",a.style.pointerEvents="none",a.style.top="0",a.style.left="0",a.style.width="100%",a.style.height="100%",a.style.zIndex="2147483647",document.body.appendChild(a));let v=Q(()=>n.getBoundingClientRect(),"getBoundingClientRect");if(!v)return s;let m=["#FF0000","#00FF00","#0000FF","#FFA500","#800080","#008080","#FF69B4","#4B0082","#FF4500","#2E8B57","#DC143C","#4682B4"],d=s%m.length,h=m[d],_=`${h}1A`,g=document.createElement("div");g.style.position="fixed",g.style.border=`2px solid ${h}`,g.style.backgroundColor=_,g.style.pointerEvents="none",g.style.boxSizing="border-box";let O={x:0,y:0};if(p){let L=p.getBoundingClientRect();O.x=L.left,O.y=L.top}let C=v.top+O.y,D=v.left+O.x;g.style.top=`${C}px`,g.style.left=`${D}px`,g.style.width=`${v.width}px`,g.style.height=`${v.height}px`;let y=document.createElement("div");y.className="playwright-highlight-label",y.style.position="fixed",y.style.background=h,y.style.color="white",y.style.padding="1px 4px",y.style.borderRadius="4px",y.style.fontSize=`${Math.min(12,Math.max(8,v.height/2))}px`,y.textContent=s.toString();let R=20,$=16,we=C+2,ye=D+v.width-R-2;(v.width<R+4||v.height<$+4)&&(we=C-$-2,ye=D+v.width-R),y.style.top=`${we}px`,y.style.left=`${ye}px`,a.appendChild(g),a.appendChild(y);let xe=()=>{let L=n.getBoundingClientRect(),j={x:0,y:0};if(p){let Me=p.getBoundingClientRect();j.x=Me.left,j.y=Me.top}let te=L.top+j.y,ne=L.left+j.x;g.style.top=`${te}px`,g.style.left=`${ne}px`,g.style.width=`${L.width}px`,g.style.height=`${L.height}px`;let _e=te+2,Te=ne+L.width-R-2;(L.width<R+4||L.height<$+4)&&(_e=te-$-2,Te=ne+L.width-R),y.style.top=`${_e}px`,y.style.left=`${Te}px`};return window.addEventListener("scroll",xe),window.addEventListener("resize",xe),s+1}finally{V("highlighting")}}function Ke(n){try{let s=document.createRange();s.selectNodeContents(n);let p=s.getBoundingClientRect();if(p.width===0||p.height===0)return!1;let a=!(p.bottom<-i||p.top>window.innerHeight+i||p.right<-i||p.left>window.innerWidth+i),v=n.parentElement;if(!v)return!1;try{return a&&v.checkVisibility({checkOpacity:!0,checkVisibilityCSS:!0})}catch{let d=window.getComputedStyle(v);return a&&d.display!=="none"&&d.visibility!=="hidden"&&d.opacity!=="0"}}catch(s){return console.warn("Error checking text node visibility:",s),!1}}function Qe(n){if(!n||!("tagName"in n))return!1;let s=new Set(["body","div","main","article","section","nav","header","footer","wx-tab-bar-wrapper"]),p=n.tagName.toLowerCase(),a=p.startsWith("wx-");return s.has(p)||a?!0:!new Set(["head","svg","script","style","link","meta","noscript","template"]).has(p)}function Ze(n){let s=Z(n);return s.height==="auto"&&s.width==="auto"?s.visibility!=="hidden"&&s.display!=="none":n.offsetWidth>0&&n.offsetHeight>0&&s.visibility!=="hidden"&&s.display!=="none"}function et(n){if(!n||n.nodeType!==Node.ELEMENT_NODE)return!1;let s=n.tagName.toLowerCase(),p=Z(n);function a(g){return g.tagName.toLowerCase()==="html"?!1:!!(p&&Le.has(p.cursor))}if(a(n))return!0;if(s==="html")return!1;if(z.has(s)){if(Se.has(p.cursor))return!1;for(let g of Re)if(n.hasAttribute(g)||n.getAttribute(g)==="true"||n.getAttribute(g)==="")return!1;return!(n.disabled||n.readOnly||n.inert)}let m=n.getAttribute("role"),d=n.getAttribute("aria-role"),h=Object.keys(n.__wxElement?.__wxEvents||{}).filter(g=>!["error","load"].includes(g)&&!g.startsWith("_"));if(n.classList&&(n.classList.contains("button")||n.classList.contains("dropdown-toggle")||n.getAttribute("data-index")||n.getAttribute("data-toggle")==="dropdown"||n.getAttribute("aria-haspopup")==="true")||z.has(s)||le.has(m||"")||le.has(d||"")||h.length>0)return!0;try{if(typeof getEventListeners=="function"){let C=getEventListeners(n),D=["click","mousedown","mouseup","dblclick"];for(let y of D)if(C[y]&&C[y].length>0)return!0}let g=window.getEventListenersForNode;if(typeof g=="function"){let C=g(n),D=["click","mousedown","mouseup","keydown","keyup","submit","change","input","focus","blur"];for(let y of D)for(let R of C)if(R.type===y)return!0}let O=["onclick","onmousedown","onmouseup","ondblclick"];for(let C of O)if(n.hasAttribute(C)||typeof n[C]=="function")return!0}catch{}return!1}function be(n){if(i===-1)return!0;let s=qe(n);if(!s||s.length===0)return!1;let p=n.ownerDocument;if(p!==window.document&&p!==N?.contentDocument)return!1;let a=n.getRootNode();if(a instanceof ShadowRoot||a===n.ownerDocument){let d=s[Math.floor(s.length/2)].left+s[Math.floor(s.length/2)].width/2,h=s[Math.floor(s.length/2)].top+s[Math.floor(s.length/2)].height/2;try{let _=Q(()=>a.elementFromPoint(d,h),"elementFromPoint");if(!_)return!1;let g=_;if(n.contains(_)||_.contains(n))return!0;for(;g&&g!==a;){if(g===n)return!0;g=g.parentElement}return!1}catch{return!0}}let v=s[Math.floor(s.length/2)].left+s[Math.floor(s.length/2)].width/2,m=s[Math.floor(s.length/2)].top+s[Math.floor(s.length/2)].height/2;try{let d=document.elementFromPoint(v,m);if(!d)return!1;let h=d;if(n.contains(d))return!0;for(;h&&h!==document.documentElement;){if(h===n)return!0;h=h.parentElement}return!1}catch{return!0}}function tt(n,s){if(s===-1)return!0;let p=n.getClientRects();if(!p||p.length===0){let a=F(n);return!a||a.width===0||a.height===0?!1:!(a.bottom<-s||a.top>window.innerHeight+s||a.right<-s||a.left>window.innerWidth+s)}for(let a of p)if(!(a.width===0||a.height===0)&&!(a.bottom<-s||a.top>window.innerHeight+s||a.right<-s||a.left>window.innerWidth+s))return!0;return!1}function vt(n){let s=n,p=0,a=0;return Q(()=>{for(;s&&s!==document.documentElement;)(s.scrollLeft||s.scrollTop)&&(p+=s.scrollLeft,a+=s.scrollTop),s=s.parentElement;return p+=window.scrollX,a+=window.scrollY,{scrollX:p,scrollY:a}},"scrollOperations")}function wt(n){return n.offsetWidth>0&&n.offsetHeight>0&&!n.hasAttribute("hidden")&&n.style.display!=="none"&&n.style.visibility!=="hidden"}function P(n,s=null){if(r&&c.nodeMetrics.totalNodes++,!n||n.id===U)return r&&c.nodeMetrics.skippedNodes++,null;if(n===document){let m={tagName:"html",attributes:{},xpath:"html",children:[]};for(let h of n.childNodes){let _=P(h,s);_&&m.children?.push(_)}let d=`${ee.current++}`;return A[d]=m,r&&c.nodeMetrics.processedNodes++,d}if(n.nodeType!==Node.ELEMENT_NODE&&n.nodeType!==Node.TEXT_NODE)return r&&c.nodeMetrics.skippedNodes++,null;if(n.nodeType===Node.TEXT_NODE){let m=n.textContent.trim();if(!m)return r&&c.nodeMetrics.skippedNodes++,null;let d=n.parentElement;if(!d||d.tagName.toLowerCase()==="script")return r&&c.nodeMetrics.skippedNodes++,null;let h=`${ee.current++}`;return A[h]={type:"TEXT_NODE",text:m,isVisible:Ke(n)},r&&c.nodeMetrics.processedNodes++,h}if(n.nodeType===Node.ELEMENT_NODE&&!Qe(n))return r&&c.nodeMetrics.skippedNodes++,null;if(i!==-1){let m=F(n),d=Z(n),h=d&&(d.position==="fixed"||d.position==="sticky"),_=n.offsetWidth>0||n.offsetHeight>0||d&&d.height==="auto"&&d.width==="auto";if(!m||!h&&!_&&(m.bottom<-i||m.top>window.innerHeight+i||m.right<-i||m.left>window.innerWidth+i))return r&&c.nodeMetrics.skippedNodes++,null}let p=k(n,!0);s&&(p=`iframe/${p}`);let a={tagName:n.tagName.toLowerCase(),attributes:{},xpath:p,children:[]};if(ke(n)||n.tagName.toLowerCase()==="iframe"||n.tagName.toLowerCase()==="body"){let m=n.getAttributeNames?.()||[],d=["style"];for(let h of m)d.includes(h)||(a.attributes[h]=n.getAttribute(h))}if(n.tagName.toLowerCase()==="wx-scroll-view"){let m=n.getAttribute("scroll-x"),d=n.getAttribute("scroll-y");a.attributes["scroll-x"]=m===null?"false":m===""?"true":m,a.attributes["scroll-y"]=d===null?"false":d===""?"true":d}if(n.nodeType===Node.ELEMENT_NODE&&(a.tagName==="wx-tab-bar-wrapper"?(a.isVisible=!0,a.isTopElement=!0,a.isInteractive=!0,a.isInViewport=!0):a.isVisible=Ze(n),window.getEventListenersForNode&&!G&&(a.listeners=window.getEventListenersForNode(n).map(m=>m.type)),(t.recordAll||t.includeOffscreenElements)&&(a.wxEvents=Object.keys(n.__wxElement?.__wxEvents||{}).filter(m=>!["error","load"].includes(m)&&!m.startsWith("_")),a.isInteractive=a.wxEvents.length>0||(a.listeners||[])?.length>0),a.isVisible&&(t.recordRects&&(a.rect=B(F(n))),a.isInViewport=tt(n,i),a.isInViewport?a.isTopElement=be(n):a.isTopElement=!1,a.isTopElement&&(a.isInteractive=et(n),a.isInteractive&&(a.highlightIndex=u++,!t.recordAll&&!t.includeOffscreenElements&&(a.wxEvents=Object.keys(n.__wxElement?.__wxEvents||{}).filter(m=>!["error","load"].includes(m)&&!m.startsWith("_"))),e&&(o>=0?o===a.highlightIndex&&Ee(n,a.highlightIndex,s):Ee(n,a.highlightIndex,s)))))),n.tagName){let m=n.tagName.toLowerCase();if(m==="iframe"&&!t.skipIframe)try{let d=n.contentDocument||n.contentWindow?.document;if(d)for(let h of d.childNodes){let _=P(h,n);_&&a.children?.push(_)}}catch(d){console.warn("Unable to access iframe:",d)}else if(n.isContentEditable||n.getAttribute("contenteditable")==="true"||n.id==="tinymce"||n.classList.contains("mce-content-body")||m==="body"&&n.getAttribute("data-id")?.startsWith("mce_"))for(let d of n.childNodes){let h=P(d,s);h&&a.children?.push(h)}else if(n.shadowRoot){a.shadowRoot=!0;for(let d of n.shadowRoot.childNodes){let h=P(d,s);h&&a.children?.push(h)}}else for(let d of n.childNodes){let h=P(d,s);h&&a.children?.push(h)}}if(a.tagName==="a"&&a.children?.length===0&&!a.attributes?.href)return r&&c.nodeMetrics.skippedNodes++,null;let v=`${ee.current++}`;return A[v]=a,r&&c.nodeMetrics.processedNodes++,v}let ve=P(document);if(I.clearCache(),r&&c){Object.keys(c.timings).forEach(p=>{c.timings[p]=c.timings[p]/1e3}),Object.keys(c.buildDomTreeBreakdown).forEach(p=>{typeof c.buildDomTreeBreakdown[p]=="number"&&(c.buildDomTreeBreakdown[p]=c.buildDomTreeBreakdown[p]/1e3)}),c.buildDomTreeBreakdown.buildDomTreeCalls>0&&(c.buildDomTreeBreakdown.averageTimePerNode=c.buildDomTreeBreakdown.totalTime/c.buildDomTreeBreakdown.buildDomTreeCalls),c.buildDomTreeBreakdown.timeInChildCalls=c.buildDomTreeBreakdown.totalTime-c.buildDomTreeBreakdown.totalSelfTime,Object.keys(c.buildDomTreeBreakdown.domOperations).forEach(p=>{let a=c.buildDomTreeBreakdown.domOperations[p],v=c.buildDomTreeBreakdown.domOperationCounts[p];v>0&&(c.buildDomTreeBreakdown.domOperations[`${p}Average`]=a/v)});let n=c.cacheMetrics.boundingRectCacheHits+c.cacheMetrics.boundingRectCacheMisses,s=c.cacheMetrics.computedStyleCacheHits+c.cacheMetrics.computedStyleCacheMisses;n>0&&(c.cacheMetrics.boundingRectHitRate=c.cacheMetrics.boundingRectCacheHits/n),s>0&&(c.cacheMetrics.computedStyleHitRate=c.cacheMetrics.computedStyleCacheHits/s),n+s>0&&(c.cacheMetrics.overallHitRate=(c.cacheMetrics.boundingRectCacheHits+c.cacheMetrics.computedStyleCacheHits)/(n+s))}return r?{rootId:ve,map:A,perfMetrics:c,isTopElement:be,getCachedBoundingRect:F}:{rootId:ve,map:A}}function Oe(t){let e=[];return Object.keys(t.map).forEach(o=>{let i=t.map[o];i.attributes||(i.attributes={}),i.isTopElement&&i.isInViewport&&e.push(i)}),e}function de(){let t=window.innerWidth/2,e=window.innerHeight/2,o=document.elementFromPoint(t,e);return o?o.contentDocument:document}function S(t,e=de()){let o=document.evaluate(t,e||document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null);return console.log("parent:",e),console.log("xPathResult:",o),o.singleNodeValue}function He(t){let o=S(t)?.getBoundingClientRect();if(!o){console.error("\u672A\u627E\u5230\u5143\u7D20");return}let{left:i,top:r,width:u,height:f}=o,l=i+u/2,w=r+f/2;return{centerX:l,centerY:w,left:i,top:r,width:u,height:f}}function lt(t){let e=t.getBoundingClientRect(),o=window.innerWidth||document.documentElement.clientWidth,i=window.innerHeight||document.documentElement.clientHeight;return e.top>=0&&e.left>=0&&e.bottom<=i&&e.right<=o}function Ae(t){t?.scrollIntoView({behavior:"instant",block:"center",inline:"center"})}function Xe(t){let e=S(t);e?Ae(e):console.error("\u672A\u627E\u5230\u5143\u7D20")}function ut(t){lt(t)?console.log("\u5143\u7D20\u5DF2\u5728\u89C6\u56FE\u4E2D\uFF0C\u65E0\u9700\u6EDA\u52A8"):Ae(t)}function Be(t){let e=S(t);if(!e){console.error("\u672A\u627E\u5230\u5143\u7D20");return}ut(e)}function q(t={doHighlightElements:!1}){let e=H({doHighlightElements:t?.doHighlightElements||!1,focusHighlightIndex:-1,viewportExpansion:0,debugMode:!1,recordRects:!0,recordAll:!1});return Oe(e).map(r=>{let{rect:u,tagName:f,wxEvents:l,listeners:w,xpath:b}=r,E={isInteractive:r.isInteractive,rect:u,tagName:f,wxEvents:l,listeners:w,xpath:b};return r.tagName?.toLowerCase()==="wx-scroll-view"&&(E.attributes=r.attributes),E})}var We=["id","xpath","textContent","attributes","wxEvents","isVisible","isInteractive","listeners"];function B(t){return{x:Math.round(t.x),y:Math.round(t.y),left:Math.round(t.left),top:Math.round(t.top),width:Math.round(t.width),height:Math.round(t.height)}}function Ve(t){let e={tagName:"root",attributes:{},children:[]};for(let o of t){if(typeof o.id>"u")continue;let i=o.xpath?.split("/").filter(f=>f.length>0);if(!i||i.length===0){console.log("\u8282\u70B9\u6CA1\u6709 xpath",o);continue}let r=e,u=o.textContent||"";for(let f=0;f<i.length;f++){let l=f===i.length-1,w=i[f];if(w.startsWith("@")){r.attributes=o.attributes||{};break}let b=r.children?.find(E=>E.tagName===w);b||(b={tagName:w,attributes:{},children:[]},r.children.push(b)),r=b,l&&We.forEach(E=>{o[E]!==void 0&&(b[E]=o[E])})}}return e.children[0]}var pt=["id","name","placeholder","data-event-opts"];function Fe(t,e){let o=["iconfont","close","guanbi","active"];return t==="class"&&e?.[t]?o.some(i=>e[t].includes(i)):t.startsWith("aria-")||t.startsWith("data-")||pt.includes(t)||t.startsWith("scroll-")}function $e(t){return{all:t=t||new Map,on:function(e,o){var i=t.get(e);i?i.push(o):t.set(e,[o])},off:function(e,o){var i=t.get(e);i&&(o?i.splice(i.indexOf(o)>>>0,1):t.set(e,[]))},emit:function(e,o){var i=t.get(e);i&&i.slice().map(function(r){r(o)}),(i=t.get("*"))&&i.slice().map(function(r){r(e,o)})}}}var dt=$e();async function je(t,e){if(pe.isAndroid){if(t==="report")return PageInfoReporterAndroid.report(e.action,e.type,e.csv,e.interactiveInfo||"",e.nativeComponentInfo||"",e.duration||0);if(t==="reportByNative")return PageInfoReporterAndroid.reportByNative(JSON.stringify(e));if(t==="recognizeImagesByNative")return PageInfoReporterAndroid.recognizeImagesByNative(e.infoStr);if(t==="getNativeComponentInfo")return PageInfoReporterAndroid.getNativeComponentInfo()}else{if(pe.isIOS)return new Promise((o,i)=>{T.debug("callNativeFunction",t,e),window.pageInfoReporter.callMethod(t,e,r=>{o(r)})});T.log("callNativeFunction not implemented for this platform",t,e)}}async function ft(t){try{let e=await je("recognizeImagesByNative",{infoStr:JSON.stringify(t.map(i=>({src:i.attributes?.src})))})||"[]",o=JSON.parse(e);o.length===t.length?t.forEach((i,r)=>{let u=o[r].label;u&&(i.attributes["aria-label"]=u)}):T.debug("\u{1F41B} \u8BC6\u522B\u56FE\u7247\u5931\u8D25\uFF0C\u957F\u5EA6\u4E0D\u5339\u914D",e)}catch(e){T.log("\u{1F41B} \u8BC6\u522B\u56FE\u7247\u5931\u8D25",e)}finally{T.debug("\u{1F41B} \u8BC6\u522B\u56FE\u7247",t.length,t,t.map(e=>e.attributes?.src)),t.forEach(e=>{e.attributes&&delete e.attributes.src})}}async function ze(t,e={recordAll:!1,includeOffscreenElements:!1}){T.log("\u{1F41B} [getReportedElements] options:",e);let o=[],i=[],r=[],u=e.includeOffscreenElements||e.recordAll;return Object.keys(t.map).forEach(f=>{let l=t.map[f];if(l.attributes||(l.attributes={}),l.children&&l.children.length&&l.children.some(E=>{let N=t.map[E].type==="TEXT_NODE";return u?N:N&&t.map[E].isVisible})&&(l.textContent=l.children.map(E=>t.map[E].text||t.map[E].el?.textContent).join(" "),delete l.children),l.id=f,(e.includeOffscreenElements||l.isInViewport)&&l.isTopElement){let b=l.isVisible&&!!l.textContent,E=l.isInteractive&&((l.wxEvents||[]).length>0||(l.listeners||[]).length>0),N=!1;l.tagName==="wx-image"&&l.attributes&&"src"in l.attributes&&(l.attributes["aria-label"]?delete l.attributes.src:r.push(l),N=!!(l.isVisible||l.isInteractive)),(b||E||N)&&i.push(l),E&&o.push(l)}}),await ft(r),{interactiveElements:o,reportedElements:i,imageElements:r}}function me(t,e=0){let o="  ".repeat(e),i=mt(t),r=t.children&&t.children.length>0,u=t.textContent||t.innerText||t.nodeValue||"",f=u!=="",l=t.tagName?.toLowerCase()||t.nodeName?.toLowerCase();if(!l)return"";l.startsWith("#")&&(l=l.slice(1));let w=`${o}<${l}${i}`;if(!r&&!f)return`${w} />
`;if(w+=">",f&&!r){let b=u;return u.includes(`
`)&&(b=u.split(`
`).join("  ")),`${w}${K(b)}</${l}>
`}return w+=`
`,t.children&&(t.children.forEach(b=>{w+=me(b,e+1)}),f&&(w+=`${o}  ${K(u)}
`)),w+=`${o}</${l}>
`,w}function mt(t){if(!t)return"";let e=[],o=t.id||t.nodeId;if(o&&e.push(`id="${o}"`),typeof t.attributes=="object"&&t.attributes)if(Array.isArray(t.attributes))t.attributes&&t.attributes.length>0&&e.push(`attr="${fe(t.attributes)}"`);else for(let u in t.attributes)t.attributes[u]!==void 0&&e.push(`${u}="${K(t.attributes[u])}"`);let i=t.events||t.wxEvents;i&&i.length>0&&e.push(`event="${fe(i)}"`);let r=t.listeners;return r&&r.length>0&&e.push(`listener="${fe(r)}"`),t.classList&&t.classList.length>0&&e.push(`class="${t.classList.join(" ")}"`),t.zIndex&&e.push(`z="${t.zIndex}"`),e.length>0?` ${e.join(" ")}`:""}function fe(t){return`[${t.map(e=>`'${K(e)}'`).join(", ")}]`}function K(t){return t?t.replace(/[&<>'"]/g,e=>{switch(e){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case'"':return"&quot;";case"'":return"&apos;";default:return e}}):""}function he(){let t=window.innerWidth/2,e=window.innerHeight/2,o=document.elementFromPoint(t,e);for(;o&&!["body","iframe"].includes(o?.tagName.toLowerCase());)o=o.parentElement;let r=o?.getBoundingClientRect();return{offsetX:r?.x||0,offsetY:r?.y||0,rect:B(r)}}function Ye(){let{offsetX:t,offsetY:e}=he(),o=q({});return o.forEach(i=>{i.rect&&(i.rect.x+=t,i.rect.y+=e)}),o}function Ge(t={doHighlightElements:!1}){let e=H({doHighlightElements:t.doHighlightElements,focusHighlightIndex:-1,viewportExpansion:0,skipIframe:!0,recordRects:!0}),o=[];return Object.keys(e.map).forEach(i=>{let r=e.map[i];r.isTopElement&&r.isInViewport&&r.isVisible&&r.isInteractive&&o.push(r)}),o.map(i=>({tagName:i.tagName,rect:i.rect}))}function ht(t){let e=t.split("/");if(e[1]==="native"&&e[2]==="tab-bar"){let i=e[3].split("tabbar-item")[1].replace("[","").replace("]","");return document.querySelector(`#container > div.tab_bar > div > div.tabbar_item.tabbar_item_${i}`)}}function Ue(t){let e=ht(t.xpath);if(!e){X.info("native element not found");return}t.event==="click"&&e.click()}function Je(){let t=[],e=S('/html/body//*[text()="\u540C\u610F"]');e&&t.push(e);let o=S('/html/body//*[text()="\u62D2\u7EDD"]');o&&t.push(o),t.forEach(i=>{i.setAttribute("event","tap")}),console.log("tapElements",t)}var gt={DEBUG_LEVEL:0,getElementByXpath:S,getCurrentDocument:de,getElementPosByXpath:He,operateNativeElement:Ue,getDomXml:Et,getAllElementsRects:q,getIframeOffset:he,getAllElementsRectsWithOffset:Ye,getAllNativeElementsRects:Ge,scrollIntoViewByXpath:Xe,scrollIntoViewIfNeededByXpath:Be};window.__WX_WM_SANDBOX__=gt;async function Et(){Je();let t=H({doHighlightElements:window.__WX_WM_SANDBOX__.DEBUG_LEVEL>=2,focusHighlightIndex:-1,viewportExpansion:0,debugMode:window.__WX_WM_SANDBOX__.DEBUG_LEVEL>=2}),{reportedElements:e}=await ze(t),o=e.map(b=>{let{attributes:E={},...N}=b,ge=Object.keys(E).reduce((V,c)=>(Fe(c,E)&&(V[c]=E[c]),V),{});return{...N,attributes:ge}}),i=Ve(o),r=me(i);X.debug("xml",r);let u=[];o.forEach(b=>{if(b.xpath?.startsWith("html/body")){let E=S(b.xpath,document)?.parentElement;E?.classList.contains("tabbar_item")&&(b.classList=E.classList,u.push(b))}}),X.debug("nativeElements",u);let f="<tab-bar>";u.forEach(b=>{let E=b.classList[1]?.replace("tabbar_item_",""),N=b.textContent;f+=`  <tabbar-item index="${E}" event="['tap']">${N}</tabbar-item>
`}),f+="</tab-bar>";let l=u.length>0?`<native>${f}</native>`:"";return`<page>
  ${r}${l}
</page>`}})();