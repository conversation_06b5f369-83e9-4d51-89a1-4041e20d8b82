"""
js 服务类
"""

import os
import uuid

from py_mini_racer import MiniRacer


class JSService:
    """
    js 服务类
    """

    def __init__(self):
        with open(
            os.path.dirname(__file__)
            + "/../resources/js_code/js_code_for_unzip_xml.js",
            "r",
            encoding="utf-8",
        ) as f:
            self.js_code_for_unzip_xml = f.read()

        with open(
            os.path.dirname(__file__) + "/../resources/js_code/js_code_xbar.js",
            "r",
            encoding="utf-8",
        ) as f:
            self.js_code_xbar = f.read()

    def decode_csv_to_xml(self, csv_data: str) -> str:
        """
        解压微信客户端上报数据的 csv 格式为 xml 格式
        """
        # 初始化 JavaScript 环境
        ctx = MiniRacer()
        ctx.eval(self.js_code_for_unzip_xml)
        # 调用 JavaScript 函数进行解码
        decoded_data = ctx.call("__WX_MER_DECODE__.decodeReportData.toXML", csv_data)
        return decoded_data

    def get_sandbox_js_api_runtime_evaluate_params(
        self, target_id: str, js_method: str
    ) -> dict:
        """
        获取调用沙箱 js api 的 runtime_evaluate 参数

        :param js_method: js api 的方法名和入参，例如 getDomXml()
        """
        return {
            "target_id": target_id,
            "method": "Runtime.evaluate",
            "params": {
                "task_id": str(uuid.uuid4()),
                "expression": f"{self.js_code_xbar}window.__WX_WM_SANDBOX__.{js_method}",
                "awaitPromise": True,
                "returnByValue": True,  # 需要返回值
            },
        }


js_service = JSService()
