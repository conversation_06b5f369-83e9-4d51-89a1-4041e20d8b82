"""
image 服务类
"""

import io
import json
import os
import re
import time
import traceback

from cffi import FFI
from PIL import Image, ImageDraw, ImageOps
from wxms.dao.redis_dao import redis_dao
from wxms.env import CONF_ENV, MODULE, WXMS_FEATURE_ENABLE_NEW_RESIZE
from wxms.logger import logger
from wxms.middleware.cos import CosClient
from wxms.middleware.llm import LlmModel
from wxms.model import APPLET_FIXED_HEIGHT, APPLET_FIXED_WIDTH, WXAMInfoDTO
from wxms.util.common_util import common_util
from wxms.util.httpx_util import httpx_util

WXAM_HEADER_DEF = """
typedef enum {
    EWxAMDecoder_None = 0,
  EWxAMDecoder_OutOfMemory = -201,
  EWxAMDecoder_InvalidData = -202,
  EWxAMDecoder_InvalidArgument = -203,
  EWxAMDecoder_NotEnoughData = -204, // not enough data to parse header
  EWxAMDecoder_UnsupportPicType =
      -205, // not support this picType, for example: bitsteam has no alpha, but
            // want APNG output
  EWxAMDecoder_BufTooShort =
      -206, // not enough data to store the decoded picture
  EWxAMDecoder_StrictBufTooShort =
      -207, // not enough data to store the decoded picture and quit immediately
  EWxAMDecoder_AtomTypeError = -208,
  EWxAMDecoder_UnsupportFrmAtomType = -209,
  EWxAMDecoder_UnsupportGenAtomType = -210,
  EWxAMDecoder_WrongRotation = -211,
  EWxAMDecoder_WrongPaletteSize = -212,
  EWxAMDecoder_WrongGridPara = -213,
  EWxAMDecoder_NotAllFrmsCanBeDec = -214,
  EWxAMDecoder_NotSupportWxAM2HEIC = -215,
  EWxAMDecoder_WrongColorRangePara = -216,
  EWxAMDecoder_WrongJPGQuality = -217,
  EWxAMDecoder_WrongOutFmt = -218,
  EWxAMDecoder_VCODEC3_INVALAD = -219,
  EWxAMDecoder_VCODEC3_NULLBUF = -220,
  EWxAMDecoder_VCODEC3_WRONGFMT = -221,
  EWxAMDecoder_VCODEC3_WRONGSIZE = -222,
  EWxAMDecoder_VCODEC3_DECFAIL = -223,
  EWxAMDecoder_VCODEC2_DECFAIL = -224,
  EWxAMDecoder_WrongAtomVer = -225,
  EWxAMDecoder_WrongHDRColorMode = -226,
  EWxAMDecoder_WrongColorSpace = -227,
  EWxAMDecoder_InternalError = -300,
  // user data err
  EWxAMDecoder_UserDataInternalError = -400,
} EWxAMDecoderErrorCode;

typedef enum {
    WxAMDecoderPicType_JPG = 0,
    WxAMDecoderPicType_PNG = 1,
    WxAMDecoderPicType_APNG = 2,
    WxAMDecoderPicType_GIF = 3,
    WxAMDecoderPicType_HEIF = 4,
} EWxAMDecoderPicType;

typedef struct {
    int i_UserDataType;
    unsigned int ui_UserDataLen;
    unsigned char *pUserDataBuf;
} SWxAMUserData;

typedef struct {
    EWxAMDecoderPicType picType;
    int i_isStrictBufMode;
    int i_compressLvl;
    SWxAMUserData stUserData;
} SWxAMDecBaseOption;

typedef struct {
  int nWidth;
  int nHeight;
  int nFrmCount; // how many frms in this seq; 1 means normal pic and >1 means
                 // animation
  int nHasAlpha; // whether this seq has Alpha; 0 means No and 1 means YES
  int nAlphaMethod; // currently, only 1 or 2 is valid
                    // 1 is for binary encoding, the alpha is neighther
                    // 0(background) nor 255(foreground) 2 is for 8 bit lossy
                    // alpha encoding
  int bProgressive; // 0 for normal static picture or animation picture
                    // 1 for progressive picture, note for progressive picture,
                    // no animation is performed.
  int nUniAnimationFlag; // whether this seq has uniformAnimation; 0 means No
                         // and 1 means YES
  int nAnimationTime; // if nUniformAnimation is YES, this is the time (10ms in
                      // ver.2 and 1ms in ver.0 & ver.1)
  int nLoopCount;

  int nDisposalFlag; // 返回2：wxam 是"兼容性模式 生成的 带P3标志位的srgb
                     // wxam"；返回3：wxam 是"最终模式 生成的 非srgb的wxam"
  int nDisposalMethod;
} SWxAMPicInfo;

int wxam_dec_wxam2pic_5(unsigned char *pWxamStream, int WxamStreamLen,
                        unsigned char *pPicStream, int *pPicLen,
                        SWxAMDecBaseOption decOption);
                        
int wxam_dec_getWXGFInfo_5(unsigned char *data, int len,
                           SWxAMPicInfo *pWxAMPicInfo);
"""


class ImageService:
    """
    image 服务类
    """

    def __init__(self):
        self.cos_client = CosClient(
            region="ap-shanghai",
            service_domain="service.cos-internal.ap-shanghai.tencentcos.cn",
            endpoint="cos-internal.ap-shanghai.tencentcos.cn",
            bucket="mmfinderdrsandbox-1258344707",
            secret_id="AKIDgBSWLyNYVFk15vWt64nGYfqy02sUr0vc",
            secret_key="VmpeEahaGhtvkKhqkPpMflcEb9SYQ3QZ",
        )
        self.llm_model_for_judging_loading_screenshot = LlmModel(
            base_url="http://drhttpsvr.polaris:8000/v1/llm-luban-waitmodel_7B_606_Qwen2.5-VL-7B-wait-0702_ck1100_export-0702-19",  # pylint: disable=C0301
            api_key="EMPTY",
        )
        self.llm_model_name_for_judging_loading_screenshot = (
            "llm-luban-waitmodel_7B_606_Qwen2.5-VL-7B-wait-0702_ck1100_export-0702-19"
        )
        self.ffi = FFI()
        self.ffi.cdef(WXAM_HEADER_DEF)
        self.lib = self.ffi.dlopen(
            os.path.dirname(__file__) + "/../resources/lib/libWxHevcDecoder.so"
        )

    def call_omniparser(self, url: str, file: str, rects: list[dict]) -> dict:
        """
        调用 omniparser 服务接口

        :param url: 服务接口的 url
        :param file: 本地文件的路径
        :param rects: 元素的框矩形信息列表
        :return: omniparser 服务接口返回处理后的结果
        """
        image_base64 = common_util.encode_image(file)
        if not image_base64 or not rects:
            return {}
        # 构造请求体
        payload = {
            "image_base64": image_base64,
            "rects": rects,
            "save_outputs": False,
            "max_area_ratio": 0.5,
        }
        res = {}
        try:
            # 发送POST请求
            response = httpx_util.send_request(
                method="POST",
                url=url,
                data=json.dumps(payload),
                headers={"Content-Type": "application/json"},
                span_name="OmniParser",
            )
            # 检查响应状态，如果请求失败则抛出异常
            response.raise_for_status()
            # 记录响应结果
            response_data = response.json()
            response_result = response_data.get("result", [])
            logger.info(
                "get omniparser response result",
                extra={"customized_data_info": {"response_result": response_result}},
            )
            for d in response_data["result"]:
                xpath = "/" + d["xpath"]
                ocr = d["paddleocr"]
                caption = d["florence2"]
                res[xpath] = ocr if ocr else caption
        except Exception as e:  # pylint: disable=W0718
            logger.error(
                "call_omniparser failed",
                extra={
                    "customized_data_info": {
                        "api_url": url,
                        "file": file,
                        "rects": rects,
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
        return res

    def check_screenshot_file(self, file: str):
        """
        检查小程序截图的本地文件的路径是否合法

        :param file: 即将保存的本地文件的路径
        """
        if not file.endswith(".jpg"):
            raise ValueError(f"wrong file type {file}")

    def check_screenshot_loading_status(self, file: str, url: str) -> bool:
        """
        判断小程序的截图是否是一张 loading 中的图片

        :param file: 本地文件的路径
        :param url: 对应文件的 url，用于 trace 上报
        :return: 小程序的截图是否是一张 loading 中的图片，True 表示不是 loading 中的图片
        """
        res = False
        try:
            prompt = """图片展示的是一个微信小程序的页面截图，请判断该页面是否加载完全，如果页面已加载完全，请回复是，如果页面没有加载完全，请回答否。
注意：1、回复需要遵从格式<think>...</think> <answer>...</answer>,其中<think>标签中是判断的理由和推理过程，<answer>标签中是判断结果，即“是”或者“否”
2、页面出现大片空白区域，认为没有加载完全，回复否；但是页面有搜索框的搜索结果为空，或者订单页面订单查询结果为无时，认为是加载完全的，回复是
3、如果页面出现白屏，则没有加载完成，回复否
4、如果页面有[跳过]字样，说明是开屏广告，没有加载完成，回复否
5、如果页面有[正在]字样，说明正在加载，没有加载完成，回复否"""
            image_content_list = [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{common_util.encode_image(file)}",
                        "customized_url_for_trace": url,
                    },
                }
            ]
            messages = [
                {
                    "role": "user",
                    "content": image_content_list + [{"type": "text", "text": prompt}],
                }
            ]
            content, _ = self.llm_model_for_judging_loading_screenshot.chat_completion(
                model=self.llm_model_name_for_judging_loading_screenshot,
                messages=messages,
                temperature=0.0,
                seed=2025,
            )
            final_answer = ""
            answer_match = re.search(r"<answer>(.*?)</answer>", content, re.DOTALL)
            if answer_match:
                final_answer = answer_match.group(1).strip()
            res = final_answer.strip() == "是"
        except Exception as e:  # pylint: disable=W0718
            logger.warning(
                "check_screenshot_loading_status failed",
                extra={
                    "customized_data_info": {
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
        return res

    def get_image_file_dir(self, app_id: str, uin: int) -> str:
        """
        生成存储图片的绝对路径的文件夹

        :param app_id: 小程序的 id
        :param uin: 微信 uin
        :return: 存储图片的绝对路径的文件夹，不以 / 结尾
        """
        root_folder = f"{os.path.dirname(__file__)}/outputs"
        time_tag = time.strftime("%Y%m%d_%H%M%S_%f")[:-3]
        res = f"{root_folder}/{app_id}/{uin}/{time_tag}_{common_util.get_random_id(8)}"
        os.makedirs(res)
        return res

    def get_image_object_key_dir(
        self, namespace: str, app_id: str, uin: int, request_id: str
    ) -> str:
        """
        生成存储图片在对象存储上的 object key 文件夹
        为了保证相同的 app_id + uin + request_id 在不同的请求中使用相同的文件夹，这里会使用 redis 进行缓存

        :param namespace: 所属的命名空间
        :param app_id: 小程序的 id
        :param uin: 微信 uin
        :param request_id: SandboxClient 对象的唯一标识，通常是 trace id
        :return: 存储图片在对象存储上的 object key 文件夹，不以 / 结尾
        """
        time_tag = time.strftime("%Y%m%d_%H")
        res = (
            f"{MODULE}/{CONF_ENV}/{namespace}"
            + f"/{app_id}/{uin}/{time_tag}/{request_id}"
        )
        if not redis_dao.image_object_key_dir_set(
            app_id=app_id,
            uin=uin,
            request_id=request_id,
            value=res,
        ):
            res_in_redis = redis_dao.image_object_key_dir_get(
                app_id=app_id,
                uin=uin,
                request_id=request_id,
            )
            logger.info(
                "get_image_object_key_dir use cache in redis",
                extra={
                    "customized_data_info": {
                        "original_value": res,
                        "cache_value": res_in_redis,
                    }
                },
            )
            res = res_in_redis
        return res

    def get_image_url(self, object_key_dir: str, file: str) -> str:
        """
        生成存储图片在对象存储上的 url

        :param object_key_dir: 存储图片在对象存储上的 object key 文件夹，用于构成 object_key
        :param file: 本地文件的路径
        :return: 存储图片在对象存储上的 url
        """
        object_key = self.__get_image_object_key(
            object_key_dir=object_key_dir, file=file
        )
        return (
            f"http://{self.cos_client.bucket}.{self.cos_client.endpoint}/{object_key}"
        )

    def image_save(
        self,
        path: str,
        image_bytes: bytes,
    ) -> tuple[tuple[int, int], tuple[int, int]]:
        """
        保存图片，resize 的步骤如下（长边短边并不是指长度长，而是指相较于固定尺寸比例的长边短边）：
        一，图片长边对齐进行 resize，从而使得宽等于 APPLET_FIXED_WIDTH 或高等于 APPLET_FIXED_HEIGHT
        二，图片左上角顶点对齐
        三，短边 padding 留白

        :param path: 保存图片的路径
        :param image_bytes: 图片的字节数据
        :return: 原始图片对应的宽高，步骤一 resize 后的宽高
        """
        image = Image.open(io.BytesIO(image_bytes))
        # 原来图片的尺寸
        real_size = image.size
        if real_size[0] * APPLET_FIXED_HEIGHT >= APPLET_FIXED_WIDTH * real_size[1] or (
            not WXMS_FEATURE_ENABLE_NEW_RESIZE
        ):
            # 下方留白
            resized_size = (
                APPLET_FIXED_WIDTH,
                round(real_size[1] * APPLET_FIXED_WIDTH / real_size[0]),
            )
        else:
            # 右方留白
            resized_size = (
                round(real_size[0] * APPLET_FIXED_HEIGHT / real_size[1]),
                APPLET_FIXED_HEIGHT,
            )
        # 保存图片
        # resize 图片
        image = image.resize(
            resized_size,
            Image.Resampling.LANCZOS,
        )
        if WXMS_FEATURE_ENABLE_NEW_RESIZE:
            # 左上角顶点对齐，短边 padding 留白
            image = ImageOps.pad(
                image,
                (APPLET_FIXED_WIDTH, APPLET_FIXED_HEIGHT),
                method=Image.Resampling.LANCZOS,
                color="white",
                centering=(0, 0),
            )
        if image.mode == "RGBA":
            # 如果图像是 RGBA 模式，转换为 RGB 后再保存为 JPEG
            image = image.convert("RGB")
        image.save(path, format="JPEG", quality=95, subsampling=0)
        logger.info(
            "save image success",
            extra={
                "customized_data_info": {
                    "path": path,
                }
            },
        )
        return real_size, resized_size

    def mark_red_point_on_image(
        self,
        file: str,
        coordinate: tuple[int, int],
        output_path: str | None = None,
        size: int = 5,
    ):
        """
        在图片上标记一个红色的点

        :param file: 本地文件的路径
        :param coordinate: 红色点的坐标 (x, y)
        :param output_path: 输出图片路径，如果为 None 则覆盖原图片
        :param size: 红色点的大小
        """
        # 打开图片
        img = Image.open(file)
        # 如果图像是 RGBA 模式，转换为 RGB
        if img.mode == "RGBA":
            img = img.convert("RGB")
        # 创建一个可以在图片上绘制的对象
        draw = ImageDraw.Draw(img)
        # 计算标记的边界框（以给定点为中心的正方形）
        x, y = coordinate
        bbox = [
            (x - size, y - size),  # 左上角
            (x + size, y + size),  # 右下角
        ]
        # 绘制红色矩形（可以改为圆形或其他形状）
        draw.ellipse(bbox, fill="red", outline="red")
        # 保存结果
        img.save(output_path if output_path else file)

    def upload_screenshot(self, object_key_dir: str, file: str) -> str:
        """
        上传本地文件到 cos

        :param object_key_dir: 存储图片在对象存储上的 object key 文件夹，用于构成 object_key
        :param file: 本地文件的路径
        :return: object 的 download url
        """
        self.check_screenshot_file(file)
        object_key = self.__get_image_object_key(
            object_key_dir=object_key_dir, file=file
        )
        return self.cos_client.upload_object(
            object_key=object_key, file=file, content_type="image/jpeg"
        )

    def wxam_convert_to_pic(
        self,
        wxam_data: bytes,
        output_type: int,
        buf_size: int,
    ) -> bytes:
        """
        将 wxam 格式的二进制字节流转换成指定图片格式的二进制字节流

        :param wxam_data: wxam 格式的二进制字节流
        :param output_type: 图片格式，详见 EWxAMDecoderPicType
        :param buf_size: 缓冲区大小用于输出二进制字节流，通常是图片大小的两倍
        :return: 指定图片格式的二进制字节流
        """
        dec_option = self.ffi.new(
            "SWxAMDecBaseOption *",
            {
                "picType": output_type,
                "i_isStrictBufMode": 0,
                "i_compressLvl": 0,
                "stUserData": {
                    "i_UserDataType": 0,
                    "ui_UserDataLen": 0,
                    "pUserDataBuf": self.ffi.NULL,
                },
            },
        )
        buf_size = buf_size if buf_size else len(wxam_data) * 2
        pic_len = self.ffi.new("int *", buf_size)  # 初始预估大小
        pic_buf = self.ffi.new("unsigned char[]", pic_len[0])
        wxam_ptr = self.ffi.from_buffer(wxam_data)
        result = self.lib.wxam_dec_wxam2pic_5(  # type: ignore
            wxam_ptr, len(wxam_data), pic_buf, pic_len, dec_option[0]
        )
        # 处理缓冲区不足
        if result == self.lib.EWxAMDecoder_BufTooShort:  # type: ignore
            pic_buf = self.ffi.new("unsigned char[]", pic_len[0])
            result = self.lib.wxam_dec_wxam2pic_5(  # type: ignore
                wxam_ptr, len(wxam_data), pic_buf, pic_len, dec_option[0]
            )
        if result != self.lib.EWxAMDecoder_None:  # type: ignore
            raise RuntimeError(f"convert_wxam_to_pic failed with error: {result}")
        return bytes(self.ffi.buffer(pic_buf, pic_len[0]))  # type: ignore

    def wxam_get_info(self, wxam_data: bytes) -> WXAMInfoDTO:
        """
        获取 wxam 格式的二进制字节流的信息

        :param wxam_data: wxam 格式的二进制字节流
        :return: 字节流的信息，字节流信息读取是否成功
        """
        info = self.ffi.new("SWxAMPicInfo *")
        wxam_ptr = self.ffi.from_buffer(wxam_data)
        result = self.lib.wxam_dec_getWXGFInfo_5(  # type: ignore
            wxam_ptr, len(wxam_data), info
        )
        if result != self.lib.EWxAMDecoder_None:  # type: ignore
            raise RuntimeError(f"wxam_get_info failed with error: {result}")
        return WXAMInfoDTO(
            frame_count=info.nFrmCount,  # type: ignore
            has_alpha=info.nHasAlpha,  # type: ignore
            height=info.nHeight,  # type: ignore
            loop_count=info.nLoopCount,  # type: ignore
            width=info.nWidth,  # type: ignore
        )

    def __get_image_object_key(self, object_key_dir: str, file: str) -> str:
        """
        生成存储图片在对象存储上的 object key

        :param object_key_dir: 存储图片在对象存储上的 object key 文件夹，用于构成 object_key
        :param file: 本地文件的路径
        :return: 存储图片在对象存储上的 object key
        """
        md5_hash = common_util.encode_image_by_md5(file)
        file_name = file.split("/")[-1]
        temp_list = file_name.split(".")
        return f"{object_key_dir}/{temp_list[0]}_{md5_hash}.{temp_list[1]}"


image_service = ImageService()

if __name__ == "__main__":
    logger.info(
        image_service.check_screenshot_loading_status(
            "/Users/<USER>/Desktop/cat.jpg",
            "https://mmfinderdrsandbox-1258344707.cos-internal.ap-shanghai.tencentcos.cn/mmfinderdrsandboxagentsvr/online/data_collection/wx56dbc76c7b340f86/2330286761/20250707_23/d6179af607694a75b8c2d416c7bcd6ca/1_standard_click_05182ab0afc5e56a1d2342fbe314b884.jpg",  # pylint: disable=C0301
        )
    )
    logger.info(
        image_service.upload_screenshot(
            image_service.get_image_object_key_dir(
                "unknown", "test_appid", 0, "00000aaa0a0a0a0"
            ),
            "/Users/<USER>/Desktop/cat.jpg",
        )
    )
