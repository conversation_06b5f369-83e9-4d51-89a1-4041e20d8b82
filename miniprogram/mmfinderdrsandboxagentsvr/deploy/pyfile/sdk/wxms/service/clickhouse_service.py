"""
clickhouse 工具类
"""

import csv
import io
import json
import traceback
from functools import wraps

import pulsar
from pydantic import BaseModel
from wxms.logger import logger
from wxms.util.common_util import CustomJSONEncoder
from wxms.util.context_util import context_util
from wxms.util.time_util import time_util


class Action(BaseModel):
    """
    action 表的一行记录，唯一键 session_id + sub_session_id + step_id + action_id
    """

    started_at: int
    ended_at: int
    session_id: str
    sub_session_id: str
    step_id: str
    action_id: str
    parent_action_id: str
    action_type: str
    parameter: str
    result: str


class Step(BaseModel):
    """
    step 表的一行记录，唯一键 session_id + sub_session_id + step_id
    """

    started_at: int
    ended_at: int
    session_id: str
    sub_session_id: str
    step_id: str
    answer: str
    answer_raw_data: str
    log_data: str
    screen: str
    result: str


class SubSession(BaseModel):
    """
    sub_session 表的一行记录，唯一键 session_id + sub_session_id
    为了简化逻辑，我们将 session 的字段压扁作为 sub_session 的字段
    """

    started_at: int
    ended_at: int
    namespace: str
    session_id: str
    sub_session_id: str
    uin: int
    app_id: str
    device_memory_size: int
    device_model: str
    device_system: str
    device_real_width: int
    device_real_height: int
    device_resized_width: int
    device_resized_height: int
    parameter: str
    from_username: str
    headless_mode: int
    run_mode: str
    result: str
    interrupt: str
    status: str


class ClickHouseService:
    """
    clickhouse 工具类
    """

    def __init__(self):
        self.pulsar_client = pulsar.Client(
            "pulsar://sz-broker.pulsar.wx.com:6650",
            authentication=pulsar.AuthenticationToken(
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ5dXlpeWl3YW5nIn0.gNTAq2kR0riLBHeQzqxa-ytQ_oTeHn69mXQpEdy2Oao"  # pylint: disable=C0301
            ),
        )
        self.pulsar_producer_for_action = self.pulsar_client.create_producer(
            "persistent://finder/luban/mmfinderdrsandbox_action",
            compression_type=pulsar.CompressionType.ZSTD,
            batching_enabled=True,
            batching_max_publish_delay_ms=500,
        )
        self.pulsar_producer_for_step = self.pulsar_client.create_producer(
            "persistent://finder/luban/mmfinderdrsandbox_step",
            compression_type=pulsar.CompressionType.ZSTD,
            batching_enabled=True,
            batching_max_publish_delay_ms=500,
        )
        self.pulsar_producer_for_sub_session = self.pulsar_client.create_producer(
            "persistent://finder/luban/mmfinderdrsandbox_sub_session",
            compression_type=pulsar.CompressionType.ZSTD,
            batching_enabled=True,
            batching_max_publish_delay_ms=500,
        )

    def add_action(self, obj: Action):
        """
        给 action 表增加一行记录
        """
        if (
            not obj.session_id
            or not obj.sub_session_id
            or not obj.step_id
            or not obj.action_id
        ):
            logger.info(
                "invalid clickhouse action",
                extra={
                    "customized_data_info": {
                        "data": obj.model_dump_json(),
                    }
                },
            )
        else:
            self.pulsar_producer_for_action.send(self.__generate_csv_string_bytes(obj))

    def add_action_decorator(self):
        """
        函数装饰器，用于给 action 表增加一行记录
        """

        def wrapper(func):
            @wraps(func)
            def inner(*args, **kwargs):
                action_type = func.__name__
                if action_type.startswith("__"):
                    action_type = action_type[2:]
                elif action_type.startswith("_"):
                    action_type = action_type[1:]
                started_at = time_util.get_millisecond_timestamp_of_current_time()
                parent_action_id = context_util.get_data_from_context(
                    "sandbox_action_id"
                )
                context_util.set_data_into_context(
                    "sandbox_action_id", context_util.get_span_id()
                )
                step_id = context_util.get_data_from_context("sandbox_step_id")
                create_new_step_flag = False
                if not step_id:
                    step_id = context_util.get_span_id()
                    context_util.set_data_into_context("sandbox_step_id", step_id)
                    create_new_step_flag = True
                res = None
                error_dict = {}
                try:
                    res = func(*args, **kwargs)
                except Exception as e:
                    error_dict = {
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                    raise e
                finally:
                    self.add_action(
                        Action(
                            started_at=started_at,
                            ended_at=time_util.get_millisecond_timestamp_of_current_time(),
                            session_id=context_util.get_trace_id(),
                            sub_session_id=context_util.get_data_from_context(
                                "sandbox_sub_session_id"
                            ),
                            step_id=context_util.get_data_from_context(
                                "sandbox_step_id"
                            ),
                            action_id=context_util.get_data_from_context(
                                "sandbox_action_id"
                            ),
                            parent_action_id=parent_action_id,
                            action_type=action_type,
                            parameter=json.dumps(kwargs, cls=CustomJSONEncoder),
                            result=json.dumps(
                                (res if not error_dict else error_dict),
                                cls=CustomJSONEncoder,
                            ),
                        )
                    )
                    context_util.set_data_into_context(
                        "sandbox_action_id", parent_action_id
                    )
                    if create_new_step_flag:
                        self.add_step(
                            Step(
                                started_at=started_at,
                                ended_at=time_util.get_millisecond_timestamp_of_current_time(),
                                session_id=context_util.get_trace_id(),
                                sub_session_id=context_util.get_data_from_context(
                                    "sandbox_sub_session_id"
                                ),
                                step_id=context_util.get_data_from_context(
                                    "sandbox_step_id"
                                ),
                                answer="[]",
                                answer_raw_data="[]",
                                log_data="[]",
                                screen="[]",
                                result=json.dumps(error_dict, cls=CustomJSONEncoder),
                            )
                        )
                        context_util.set_data_into_context("sandbox_step_id", "")

                return res

            return inner

        return wrapper

    def add_step(self, obj: Step):
        """
        给 step 表增加一行记录
        """
        if not obj.session_id or not obj.sub_session_id or not obj.step_id:
            logger.info(
                "invalid clickhouse step",
                extra={
                    "customized_data_info": {
                        "data": obj.model_dump_json(),
                    }
                },
            )
        else:
            self.pulsar_producer_for_step.send(self.__generate_csv_string_bytes(obj))

    def add_sub_session(self, obj: SubSession):
        """
        给 sub_session 表增加一行记录
        """
        if not obj.session_id or not obj.sub_session_id:
            logger.info(
                "invalid clickhouse sub_session",
                extra={
                    "customized_data_info": {
                        "data": obj.model_dump_json(),
                    }
                },
            )
        else:
            self.pulsar_producer_for_sub_session.send(
                self.__generate_csv_string_bytes(obj)
            )

    def flush(self):
        """
        将缓存中的数据都写入 pulsar 中
        """
        self.pulsar_producer_for_action.flush()
        self.pulsar_producer_for_step.flush()
        self.pulsar_producer_for_sub_session.flush()
        self.pulsar_client.close()

    def __generate_csv_string_bytes(self, obj: BaseModel) -> bytes:
        obj_dict = obj.model_dump()
        obj_list_header = []
        obj_list_data = []
        for key, value in obj_dict.items():
            obj_list_header.append(key)
            obj_list_data.append(value)
        output = io.StringIO()
        csv_writer = csv.writer(output)
        csv_writer.writerow(obj_list_header)
        csv_writer.writerow(obj_list_data)
        csv_string = output.getvalue()
        output.close()
        return csv_string.encode("utf-8")


clickhouse_service = ClickHouseService()
