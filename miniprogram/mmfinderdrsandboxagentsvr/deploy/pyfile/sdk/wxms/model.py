"""
定义各种类型
"""

import uuid
from enum import IntEnum

from pydantic import BaseModel, Field

APPLET_FIXED_HEIGHT = 776
APPLET_FIXED_WIDTH = 410
INTERRUPT_ERROR_CODE_DICT = {
    -1: "小程序不在前台",
    -2: "小程序不在 agent 模式（通过非 launchApplet 的形式启动过小程序）",
    -3: "命令未绑定到小程序",
    -4: "小程序 webview 不存在",
    # -5: "命令运行错误",
    # -6: "命令参数错误",
    -7: "待拉起收银台，中断操作",
    -8: "不支持 skyline",
    -9: "用户中断",
}


class InterruptError(Exception):
    """
    中断错误，调用方需要处理该错误用于中断推理过程
    """

    def __init__(self, msg="interrupted", params=None):
        self.msg = msg
        super().__init__(self.msg)
        self.params = params


class ScreenshotDTO(BaseModel):
    """
    截图的数据传输类
    """

    file: str
    real_size: tuple[int, int]
    resized_size: tuple[int, int]
    url: str


class SandboxActionMessageStyleType(IntEnum):
    """
    沙箱操作消息的样式类型
    """

    TITLE = 0
    SUBTITLE = 1


class SandboxActionMessage(BaseModel):
    """
    沙箱操作消息
    """

    content: str
    process_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    style_type: SandboxActionMessageStyleType


class SandboxActionStandardConfig(BaseModel):
    """
    沙箱操作的标准配置
    """

    action_message_list_after_action: list[SandboxActionMessage] | None = None
    action_message_list_before_action: list[SandboxActionMessage] | None = None
    dom_xml_disabled: bool = False
    dom_xml_include_offscreen_elements: bool = False
    elements_rects_disabled: bool = False


class SandboxActionStandardOutput(BaseModel):
    """
    沙箱操作的标准输出
    """

    applet_page_info: dict
    dom_xml: dict
    elements_rects: dict
    native_elements_rects: dict
    screenshot_dto: ScreenshotDTO


class WXAMInfoDTO(BaseModel):
    """
    wxam 格式的二进制字节流的信息传输类
    """

    frame_count: int
    has_alpha: int
    height: int
    loop_count: int
    width: int
