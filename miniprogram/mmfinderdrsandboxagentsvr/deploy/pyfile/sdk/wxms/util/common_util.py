"""
通常工具类
"""

import base64
import hashlib
import json
import re
import secrets
import time
import traceback
from typing import Callable, ParamSpec, TypeVar

from pydantic import BaseModel
from wxms.logger import logger

P = ParamSpec("P")  # 用于捕获函数参数的类型
R = TypeVar("R")  # 用于捕获函数返回值的类型


class CustomJSONEncoder(json.JSONEncoder):
    """
    自定义的 JSONEncoder
    """

    def default(self, o):
        if isinstance(o, BaseModel):
            return o.model_dump()
        return super().default(o)


class CommonUtil:
    """
    通常工具类
    """

    def __init__(self):
        pass

    def base64_decode(self, s: str) -> str:
        """
        base64 解码

        :param s: 编码后的字符串
        :return: base64 解码后的字符串
        """
        return base64.b64decode(s).decode()

    def base64_encode(self, s: str | bytes) -> str:
        """
        base64 编码

        :param s: 原字符串或字节数组
        :return: base64 编码后的字符串
        """
        if isinstance(s, str):
            s = s.encode()
        return base64.b64encode(s).decode()

    def encode_image(self, path: str) -> str:
        """
        读取图片文件并编码为 base64 字符串

        :param path: 保存图片的路径
        :return: base64 字符串
        """
        with open(path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode("utf-8")

    def encode_image_by_md5(self, path: str) -> str:
        """
        读取图片文件并并计算 MD5 哈希值

        :param path: 保存图片的路径
        :return: 16进制格式的 MD5 哈希字符串
        """
        # 读取图片文件为bytes
        with open(path, "rb") as image_file:
            return hashlib.md5(image_file.read()).hexdigest()

    def execute_function_with_retry(
        self,
        func: Callable[P, R],
        func_max_retry: int = 3,
        func_delay: float = 0.5,
        func_exceptions: tuple = (Exception,),
        /,
        *args: P.args,
        **kwargs: P.kwargs,
    ) -> R:
        """
        重试运行函数

        :param func: 被执行函数
        :param func_max_retry: 重试次数
        :param func_delay: 重试的间隔时间，单位秒
        :param func_exceptions: 指定被允许重试的错误
        :param *args: 被执行函数的入参
        :param **kwargs: 被执行函数的入参
        :return: 被执行函数的返回
        """
        last_exception = Exception(f"unknown error in {func.__name__}")
        for attempt_count in range(1, func_max_retry + 1):
            try:
                return func(*args, **kwargs)
            except func_exceptions as e:
                last_exception = e
                if attempt_count == func_max_retry:
                    logger_method = logger.error
                else:
                    logger_method = logger.warning
                logger_method(
                    "execute_function_with_retry failed",
                    extra={
                        "customized_data_info": {
                            "attempt_count": attempt_count,
                            "max_retry": func_max_retry,
                            "func": func.__name__,
                            "exception": str(e),
                            "traceback": traceback.format_exc(),
                        }
                    },
                )
            time.sleep(func_delay)
        raise last_exception

    def extract_xml_tag(self, content: str, tag: str) -> str:
        """
        从 content 中提取指定 XML 标签中的内容

        :param content: 被提取内容的原始内容
        :param tag: XML 标签名
        :return: 指定 XML 标签中的内容
        """
        pattern = f"<{tag}>(.*?)</{tag}>"
        match = re.search(pattern, content, re.DOTALL)
        res = ""
        if match:
            res = match.group(1).strip()
        return res

    def get_random_id(self, size: int) -> str:
        """
        随机生成 id

        :param size: random id 的长度
        :return: random id
        """
        return secrets.token_hex(size // 2)


common_util = CommonUtil()

if __name__ == "__main__":
    logger.info(common_util.get_random_id(32))
    logger.info(common_util.base64_decode("d3g2NzQ0M2ZmMTUxNmEyNTkz"))

    def __test_func(max_retry: int) -> str:
        return f"{max_retry}"

    logger.info(common_util.execute_function_with_retry(__test_func, max_retry=2))
