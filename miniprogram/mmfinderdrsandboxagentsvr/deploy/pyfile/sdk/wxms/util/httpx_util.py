"""
httpx 工具类
"""

import traceback

import httpx
from httpx import create_ssl_context
from opentelemetry import trace
from wxms.logger import logger
from wxms.util.context_util import context_util


class HttpxUtil:
    """
    httpx 工具类
    """

    def __init__(self):
        self.httpx_ssl_context = create_ssl_context()
        self.client = httpx.Client(
            limits=httpx.Limits(
                max_connections=100,  # 最大连接数
                max_keepalive_connections=50,  # 最大保持活跃的连接数
                keepalive_expiry=30,  # 保持连接的时间（秒）
            ),
            timeout=30.0,  # 超时设置
        )
        self.client_async = httpx.AsyncClient(
            limits=httpx.Limits(
                max_connections=100,  # 最大连接数
                max_keepalive_connections=50,  # 最大保持活跃的连接数
                keepalive_expiry=30,  # 保持连接的时间（秒）
            ),
            timeout=30.0,  # 超时设置
        )

    def send_request(
        self,
        method: str,
        url: str,
        content=None,
        data=None,
        files=None,
        json=None,
        params=None,
        headers: dict | None = None,
        timeout: int | float | None = None,
        retry_count=0,
        span_name: str | None = None,
    ) -> httpx.Response:
        """
        发送 http 请求
        """
        if span_name is None:
            span_name = method.upper()
        else:
            span_name = f"{method.upper()} {span_name}"
        if headers is None:
            headers = {}

        @context_util.add_trace_span(span_name=span_name)
        def __inner():
            current_span = trace.get_current_span()
            current_span.set_attribute("http.method", method.upper())
            current_span.set_attribute("http.url", url)
            context_util.get_and_inject_span_context(headers)
            res = None
            func = getattr(self.client, method.lower())
            if not func:
                raise ValueError(f"not support method: {method}")
            for i in range(retry_count + 1):
                try:
                    res = self.client.request(
                        method=method,
                        url=url,
                        content=content,
                        data=data,
                        files=files,
                        json=json,
                        params=params,
                        headers=headers,
                        timeout=timeout,
                    )
                    break
                except Exception as e:  # pylint: disable=W0718
                    logger.warning(
                        "send request failed",
                        extra={
                            "customized_data_info": {
                                "url": url,
                                "data": data,
                                "exception": str(e),
                                "traceback": traceback.format_exc(),
                                "retry_count": i,
                            }
                        },
                    )
            if res is None:
                raise ValueError("send request res is None")
            current_span.set_attribute("http.status_code", res.status_code)
            if res.status_code != 200:
                current_span.set_status(trace.Status(trace.StatusCode.ERROR))
            return res

        return __inner()

    async def send_request_async(
        self,
        method: str,
        url: str,
        content=None,
        data=None,
        files=None,
        json=None,
        params=None,
        headers: dict | None = None,
        timeout: int | float | None = None,
        retry_count=0,
        span_name: str | None = None,
    ) -> httpx.Response:
        """
        发送 http 请求（异步）
        """
        if span_name is None:
            span_name = method.upper()
        else:
            span_name = f"{method.upper()} {span_name}"
        if headers is None:
            headers = {}

        @context_util.add_trace_span_for_async(span_name=span_name)
        async def __inner():
            current_span = trace.get_current_span()
            current_span.set_attribute("http.method", method.upper())
            current_span.set_attribute("http.url", url)
            context_util.get_and_inject_span_context(headers)
            res = None
            func = getattr(self.client_async, method.lower())
            if not func:
                raise ValueError(f"not support method: {method}")
            for i in range(retry_count + 1):
                try:
                    res = await self.client_async.request(
                        method=method,
                        url=url,
                        content=content,
                        data=data,
                        files=files,
                        json=json,
                        params=params,
                        headers=headers,
                        timeout=timeout,
                    )
                    break
                except Exception as e:  # pylint: disable=W0718
                    logger.warning(
                        "send request async failed",
                        extra={
                            "customized_data_info": {
                                "url": url,
                                "data": data,
                                "exception": str(e),
                                "traceback": traceback.format_exc(),
                                "retry_count": i,
                            }
                        },
                    )
            if res is None:
                raise ValueError("send request async res is None")
            current_span.set_attribute("http.status_code", res.status_code)
            if res.status_code != 200:
                current_span.set_status(trace.Status(trace.StatusCode.ERROR))
            return res

        return await __inner()


httpx_util = HttpxUtil()
