"""
prompt 工具类
"""

import os


class PromptUtil:
    """
    prompt 工具类
    """

    def __init__(self):
        pass

    def get_prompts_dict(self, folder_path: str) -> dict[str, dict[str, str]]:
        """
        prompt 文件所在的目录，要求 prompt 文件在该目录的二级子目录下
        例如入参为 /home/<USER>/，则需要 prompt 文件所在位置为 /home/<USER>/folder/prompt_a

        :param folder_path: prompt 文件所在的目录
        :return: 一个二级字典，取 prompt 的时候需要二级取，例如 res["folder"]["prompt_a"]
        """
        res = {}
        file_name_list = os.listdir(folder_path)
        for file_name in file_name_list:
            sub_path = folder_path + file_name + "/"
            res[file_name] = {}
            sub_file_name_list = os.listdir(sub_path)
            for sub_file_name in sub_file_name_list:
                with open(sub_path + sub_file_name, "r", encoding="utf-8") as f:
                    content = f.read()
                    res[file_name][sub_file_name] = content
        return res


prompt_util = PromptUtil()
