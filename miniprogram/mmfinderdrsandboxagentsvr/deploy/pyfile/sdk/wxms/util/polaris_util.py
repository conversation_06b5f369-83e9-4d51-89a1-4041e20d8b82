"""
北极星工具类
"""

import traceback

from polaris.api.consumer import (
    GetOneInstanceRequest,
    create_consumer_by_default_config_file,
)
from wxms.logger import logger


class PolarisUtil:
    """
    北极星工具类
    """

    def __init__(self):
        self.polaris_consumer_api = create_consumer_by_default_config_file()

    def get_service_address(
        self, service: str, namespace: str, default_value: str
    ) -> str:
        """
        获取服务域名
        """
        request = GetOneInstanceRequest(service=service, namespace=namespace)
        try:
            instance = self.polaris_consumer_api.get_one_instance(request)
            return f"{instance.get_host()}:{instance.get_port()}"
        except Exception as e:  # pylint: disable=W0718
            logger.warning(
                "get polaris all instances failed",
                extra={
                    "customized_data_info": {
                        "service": service,
                        "namespace": namespace,
                        "default_value": default_value,
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
            return default_value


polaris_util = PolarisUtil()
