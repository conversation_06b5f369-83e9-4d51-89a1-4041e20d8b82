"""
redis 数据访问类
"""

from enum import IntEnum

from wxms.middleware.redis_pool import RedisPool


class RedisDBNum(IntEnum):
    """
    redis 的数据库编号
    """

    DEFAULT = 0
    APPLET_XPATH_PROMPT = 1
    IMAGE_OBJECT_KEY_DIR = 2


class RedisDAO:
    """
    redis 数据访问类
    """

    def __init__(self):
        self.redis_pool = RedisPool(
            host="************",
            port=6379,
            password="WTlL6a5jf80o2VQB",
            init_dbs=list(set([i.value for i in RedisDBNum])),
        )

    def image_object_key_dir_get(
        self,
        app_id: str,
        uin: int,
        request_id: str,
    ) -> str:
        """
        获取存储图片在对象存储上的 object key 文件夹，用于构成 object_key

        :param app_id: 小程序的 id
        :param uin: 微信 uin
        :param request_id: SandboxClient 对象的唯一标识，通常是 trace id
        :return: 存储图片在对象存储上的 object key 文件夹
        """
        client = self.redis_pool.get_redis(RedisDBNum.IMAGE_OBJECT_KEY_DIR.value)
        cache_key = f"image_object_key_dir:{app_id}:{uin}:{request_id}"
        temp_res = client.get(cache_key)
        return str(temp_res, encoding="utf-8") if temp_res is not None else ""  # type: ignore

    def image_object_key_dir_set(
        self, app_id: str, uin: int, request_id: str, value: str
    ) -> bool:
        """
        设置存储图片在对象存储上的 object key 文件夹，缓存时间设置2天，所以不能有一个操作

        :param app_id: 小程序的 id
        :param uin: 微信 uin
        :param request_id: SandboxClient 对象的唯一标识，通常是 trace id
        :param value: 存储图片在对象存储上的 object key 文件夹
        :return: 是否设置成功
        """
        client = self.redis_pool.get_redis(RedisDBNum.IMAGE_OBJECT_KEY_DIR.value)
        cache_key = f"image_object_key_dir:{app_id}:{uin}:{request_id}"
        return bool(client.set(cache_key, value, ex=60 * 60 * 24 * 2, nx=True))


redis_dao = RedisDAO()

if __name__ == "__main__":
    from wxms.logger import logger

    temp_value = redis_dao.image_object_key_dir_get("app_id", 0, "request_id")
    logger.info(temp_value)
    logger.info(type(temp_value))
    logger.info(redis_dao.image_object_key_dir_set("app_id", 0, "request_id", "1"))
    temp_value = redis_dao.image_object_key_dir_get("app_id", 0, "request_id")
    logger.info(temp_value)
    logger.info(type(temp_value))
