"""
v3 推理逻辑
"""

import traceback

from model import <PERSON><PERSON>n<PERSON>, PythonResult, SandboxAgentBase
from wxms.logger import logger
from wxms.middleware.llm import LlmModel
from wxms.model import (
    SandboxActionMessage,
    SandboxActionMessageStyleType,
    SandboxActionStandardConfig,
)
from wxms.service.sandbox_service import SandboxClient
from wxms.util.common_util import common_util
from wxms.util.context_util import TracedThreadPoolExecutor


class SandboxAgent(SandboxAgentBase):
    """
    v3 推理逻辑的沙箱 Agent 推理类
    """

    def __init__(
        self,
        python_input: PythonInput,
        python_result: PythonResult,
        sandbox_client: SandboxClient,
    ):
        super().__init__(python_input, python_result, sandbox_client)
        # 初始化所有需要跨步保存的状态
        self.__thread_pool = TracedThreadPoolExecutor(max_workers=10)
        self.special_app_id_list_v1 = (
            self.python_input.special_str_1.split(",")
            if self.python_input.special_str_1
            else []
        )
        self.summary_all_v1 = ""
        self.pre_action_summary = ""
        self.pre_action_is_valid = True
        self.golf_flag = False
        if self.python_input.app_id in self.special_app_id_list_v1:
            self.golf_flag = True

    def run_one_step_internal(self) -> bool:
        res = False
        if (
            len(self.sandbox_client.standard_output_list) >= 2
            and self.pre_action_is_valid
        ):
            if len(self.summary_all_v1) > 0:
                self.summary_all_v1 = (
                    self.summary_all_v1
                    + "; "
                    + str(len(self.sandbox_client.standard_output_list) - 1)
                    + "."
                    + self.pre_action_summary
                )
            else:
                self.summary_all_v1 = (
                    self.summary_all_v1
                    + str(len(self.sandbox_client.standard_output_list) - 1)
                    + "."
                    + self.pre_action_summary
                )
        # 先调用模型推理下一步的 action，同时等待图片的 loading 判断结果
        next_screenshot_file_path = self.sandbox_client.standard_output_list[
            -1
        ].screenshot_dto.file
        get_error_signal = False
        future = None
        while next_screenshot_file_path and not get_error_signal:
            last_screenshot_file_path = self.sandbox_client.standard_output_list[
                -1
            ].screenshot_dto.file
            future = self.__thread_pool.submit(self.__get_action_answer_content)
            next_screenshot_file_path, get_error_signal = (
                self.sandbox_client.get_image_loading_status_signal(
                    last_screenshot_file_path
                )
            )
        if future is None:
            raise ValueError("future is None")
        answer_content = future.result()
        self.sandbox_client.get_image_loading_status_signal("final")
        self.python_result.answers_raw_data.append(self.summary_all_v1)
        self.python_result.answers_raw_data.append(answer_content)

        action_7b = common_util.extract_xml_tag(content=answer_content, tag="answer")
        action_7b = action_7b.replace("\n", "")
        self.python_result.answers.append(action_7b)
        action_summary = common_util.extract_xml_tag(
            content=answer_content, tag="summary"
        )
        action_summary = action_summary.replace("\n", "")

        sandbox_action_standard_config = SandboxActionStandardConfig(
            action_message_list_before_action=(
                [
                    SandboxActionMessage(
                        style_type=SandboxActionMessageStyleType.SUBTITLE,
                        content=action_summary,
                    )
                ]
                if action_summary
                else None
            ),
            dom_xml_disabled=True,
            elements_rects_disabled=True,
        )
        self.pre_action_is_valid = True
        if action_7b.startswith("click"):
            parts = action_7b.replace(",", " ").split()
            x = int(parts[1])
            y = int(parts[2])
            self.sandbox_client.standard_click(
                coordinate=(x, y), config=sandbox_action_standard_config
            )
        elif action_7b.startswith("input"):
            parts = action_7b.replace(",", " ").split()
            x = int(parts[1])
            y = int(parts[2])
            text = parts[3]
            self.sandbox_client.standard_input(
                text=text, coordinate=(x, y), config=sandbox_action_standard_config
            )
        elif action_7b.startswith("scroll"):
            coordinate = None
            delta_x = 0
            delta_y = 0
            if action_7b.startswith("scrollleft"):
                parts = action_7b.replace(",", " ").split()
                x = int(parts[1])
                y = int(parts[2])
                coordinate = (x, y)
                delta_x = 250
            elif action_7b.startswith("scrollright"):
                parts = action_7b.replace(",", " ").split()
                x = int(parts[1])
                y = int(parts[2])
                coordinate = (x, y)
                delta_x = -250
            else:
                delta_y = int(action_7b.split(" ", 1)[1])
            self.sandbox_client.standard_scroll(
                coordinate=coordinate,
                delta_x=delta_x,
                delta_y=delta_y,
                config=sandbox_action_standard_config,
            )
        elif action_7b.startswith("wait"):
            self.sandbox_client.standard_wait(
                seconds=1, config=sandbox_action_standard_config
            )
        elif action_7b == "finish" or action_7b == "stop":
            res = True
        else:
            logger.warning(
                "unexpected answer content",
                extra={"customized_data_info": {"answer_content": answer_content}},
            )
            self.pre_action_is_valid = False
        self.pre_action_summary = action_summary
        return res

    def __get_action_answer_content(self) -> str:
        base_url = self.python_input.vlt_base_url_v1
        model_name = self.python_input.vlt_model_name_v1
        prompt = self.python_input.prompt_vlt_v1
        instruction = self.python_input.instruction
        if self.golf_flag:
            base_url = self.python_input.base_url
            model_name = self.python_input.model_name
            prompt = self.python_input.prompt

        input_img_dto_list = [
            i.screenshot_dto for i in self.sandbox_client.standard_output_list
        ][-2:]
        answer_content = ""
        try:
            llm_model = LlmModel(
                base_url=base_url,
                api_key="EMPTY",
            )
            input_imgs_content = [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{common_util.encode_image(img_dto.file)}",
                        "customized_url_for_trace": img_dto.url,
                    },
                }
                for img_dto in input_img_dto_list
            ]
            messages = [
                {
                    "role": "user",
                    "content": input_imgs_content
                    + [
                        {
                            "type": "text",
                            "text": prompt.format(instruction, self.summary_all_v1),
                        }
                    ],
                }
            ]
            answer_content, _ = llm_model.chat_completion(
                model=model_name,
                messages=messages,
                temperature=0.0,
                seed=2025,
            )
        except Exception as e:  # pylint: disable=W0718
            logger.error(
                "__get_action_answer_content failed",
                extra={
                    "customized_data_info": {
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
        return answer_content
