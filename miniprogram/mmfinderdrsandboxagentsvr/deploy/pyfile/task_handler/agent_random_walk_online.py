"""
随机游走
"""

import json
import random
import traceback

from model import <PERSON>In<PERSON>, PythonResult, SandboxAgentBase
from wxms.logger import logger
from wxms.middleware.llm import LlmModel
from wxms.model import SandboxActionStandardOutput, ScreenshotDTO
from wxms.service.sandbox_service import SandboxClient
from wxms.util.common_util import common_util


class SandboxAgent(SandboxAgentBase):
    """
    随机游走的沙箱 Agent 推理类
    """

    def __init__(
        self,
        python_input: PythonInput,
        python_result: PythonResult,
        sandbox_client: SandboxClient,
    ):
        super().__init__(python_input, python_result, sandbox_client)
        # 初始化所有需要跨步保存的状态
        self.scroll_flag = False

    def run_one_step_internal(self) -> bool:
        # Step 1: 截图 before
        last_step_standard_output = self.sandbox_client.standard_output_list[-1]
        # Step 2: 随机获取可操作元素
        rects = self.__parse_rect_data(
            last_step_standard_output.elements_rects
        ) + self.__parse_rect_data(last_step_standard_output.native_elements_rects)
        if len(rects) == 0 and self.scroll_flag is True:
            # 没有可操作元素且上一步骤是 scroll 操作
            return True
        rects.extend(
            [
                {
                    "action": "scroll",
                    "id": "scroll",
                    "rect": [
                        200,
                        200,
                        random.randint(100, 500),
                        random.randint(100, 500),
                    ],
                },
                {
                    "action": "scroll",
                    "id": "scroll",
                    "rect": [
                        200,
                        200,
                        random.randint(100, 500),
                        random.randint(100, 500),
                    ],
                },
            ]
        )
        selected_rect = random.choice(rects)
        # Step 3: 执行操作
        logger.info(
            "start action",
            extra={"customized_data_info": {"selected_rect": selected_rect}},
        )
        self.scroll_flag = False
        if selected_rect["action"] == "click":
            rect = selected_rect["rect"]
            x_center = int((rect[0] + rect[2]) // 2)
            y_center = int((rect[1] + rect[3]) // 2)
            self.sandbox_client.standard_click(coordinate=(x_center, y_center))
            selected_action = f"click {str(x_center)} {str(y_center)}"
        elif selected_rect["action"] == "input":
            rect = selected_rect["rect"]
            x_center = int((rect[0] + rect[2]) // 2)
            y_center = int((rect[1] + rect[3]) // 2)
            prompt = f"""你是一个多模态搜索词预测专家。请严格按以下步骤分析：
1. **视觉定位**：
- 已收到用户提供的界面截图和点击坐标 (x={x_center}, y={y_center})。
- 定位该坐标所在的UI元素（优先判断是否为输入框，其次检查附近文本/图标）。

2. **场景推理**（若为输入框）：
- 识别整个界面的功能类型（如电商/音乐/地图/社交）。
- 提取输入框附近的视觉线索：
    * 占位符文字（如"搜索商品"）
    * 关联图标（如放大镜、定位pin）
    * 周围功能区（如搜索历史、热门推荐）

3. **生成合理输入**：
- 根据场景和线索生成3-5个最可能的输入，要求：
    * 符合该场景高频需求（如电商→"蓝牙耳机"）
    * 若界面有推荐词，优先选择同类型的其他发散性的搜索词
    * 长度≤5个汉字或2个英文单词
- 若坐标非输入框，找到附近的输入框进行判断

4. **输出格式**（JSON）：{{
"ui_element": "input_box/button/other",  // 点击的UI元素类型
"context": "占位符文字或功能描述",       // 关键上下文
"recommended_queries": ["词1", "词2"],  // 推荐的搜索词
"confidence": 0-1                      // 置信度(1为最高)
}}
- 严格按照输出格式输出，仅仅需要输出 json格式内容，不要带其他任何内容

当前坐标：x={x_center}, y={y_center}（请开始分析）
"""
            answer_content = self.__get_input_action_content(
                last_step_standard_output.screenshot_dto, prompt
            )
            text = ""
            try:
                result = json.loads(
                    answer_content.replace("```", "")
                    .replace("json", "")
                    .replace("```", "")
                    .strip()
                )
                text = random.choice(result.get("recommended_queries"))
            except Exception as e:  # pylint: disable=W0718
                logger.error(
                    "input failed",
                    extra={
                        "customized_data_info": {
                            "exception": str(e),
                            "traceback": traceback.format_exc(),
                        }
                    },
                )
            self.sandbox_client.standard_input(
                text=text, coordinate=(x_center, y_center)
            )
            selected_action = f"input {x_center} {y_center} {text}"
        elif selected_rect["action"] == "scroll":
            rect = selected_rect["rect"]
            x = int(rect[2])
            self.sandbox_client.standard_scroll(delta_x=0, delta_y=x)
            selected_action = f"scroll {x}"
            self.scroll_flag = True
        else:
            logger.warning(
                "不支持的操作类型，使用 wait 操作",
                extra={"customized_data_info": {"selected_rect": selected_rect}},
            )
            selected_action = "wait"
            self.sandbox_client.standard_wait(seconds=1.0)
        # Step 4: 记录日志
        new_step_standard_output = self.sandbox_client.standard_output_list[-1]
        self.__log_step(
            before_step_standard_output=last_step_standard_output,
            action=selected_action,
            after_step_standard_output=new_step_standard_output,
            selected_rect=selected_rect,
        )
        return False

    def __get_input_action_content(
        self,
        before_image_dto: ScreenshotDTO,
        prompt: str,
    ) -> str:
        answer_content = ""
        try:
            llm_model = LlmModel(
                base_url="http://drhttpsvr.polaris:8000/v1/llm-luban-xcx-Qwen2.5-VL-72B-Instruct",
                api_key="EMPTY",
            )
            answer_content, _ = llm_model.chat_completion(
                model="llm-luban-xcx-Qwen2.5-VL-72B-Instruct",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{common_util.encode_image(before_image_dto.file)}",  # pylint: disable=C0301
                                    "customized_url_for_trace": before_image_dto.url,
                                },
                            },
                            {"type": "text", "text": prompt},
                        ],
                    }
                ],
                temperature=0.9,
                seed=2025,
            )
        except Exception as e:  # pylint: disable=W0718
            logger.error(
                "__get_input_action_content failed",
                extra={
                    "customized_data_info": {
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
        return answer_content

    def __log_step(
        self,
        before_step_standard_output: SandboxActionStandardOutput,
        action: str,
        after_step_standard_output: SandboxActionStandardOutput,
        selected_rect: dict,
    ):
        data = {
            "before_image": before_step_standard_output.screenshot_dto.url,
            "before_path_url": before_step_standard_output.applet_page_info.get(
                "data", {}
            )
            .get("data", {})
            .get("path", ""),
            "action": action,
            "after_image": after_step_standard_output.screenshot_dto.url,
            "after_path_url": after_step_standard_output.applet_page_info.get(
                "data", {}
            )
            .get("data", {})
            .get("path", ""),
            "rect": selected_rect["rect"],
            "task_id": self.sandbox_client.request_id,
        }
        self.python_result.answers.append(json.dumps(data, ensure_ascii=False))

    def __parse_rect_data(self, raw_data: dict) -> list[dict]:
        result = []
        if not raw_data or raw_data.get("code") != 0:
            logger.error("框数据获取失败")
            return result

        elements = (
            raw_data.get("data", {})
            .get("result", {})
            .get("result", {})
            .get("value", [])
        )
        for idx, item in enumerate(elements):
            listeners = item.get("listeners", [])
            wx_events = item.get("wxEvents", [])
            type_flag = False
            for i in listeners:
                if i in ["click", "focus", "blur", "change", "input"]:
                    type_flag = True
                    break
            for i in wx_events:
                if i in ["tap", "focus", "blur", "change", "input"]:
                    type_flag = True
                    break
            if not type_flag or not item.get("isInteractive", True):
                continue
            rect_info = item.get("rect")
            tag_name = item.get("tagName", "unknown")
            if rect_info and all(k in rect_info for k in ["x", "y", "width", "height"]):
                x = rect_info["x"]
                y = rect_info["y"]
                width = rect_info["width"]
                height = rect_info["height"]
                x1 = x + width
                y1 = y + height
                # 过滤太大的操作框
                if width * height > 300 * 300:
                    continue
                # 判断 action 类型
                if tag_name == "wx-input":
                    action = "input"
                else:
                    action = "click"
                # 转换为 [x, y, width, height] 格式
                result.append(
                    {
                        "action": action,
                        "id": f"{tag_name}-{idx}",
                        "rect": [int(x), int(y), int(x1), int(y1)],
                    }
                )
        return result
