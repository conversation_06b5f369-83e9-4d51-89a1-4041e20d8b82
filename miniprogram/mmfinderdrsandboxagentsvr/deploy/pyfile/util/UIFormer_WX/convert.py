#!/usr/bin/env python3
# convert_wechat_minihtml_to_xml.py  (lxml 版本)

import re, sys
from pathlib import Path
import xml.etree.ElementTree as ET
from lxml import html, etree

# ──────────────────────────────────────────
# 1. 预清洗：同旧版
# ──────────────────────────────────────────

def sanitize_html(raw: str) -> str:
    txt = raw.replace("id=", " id=").replace("event=", " event=")
    return txt

# ──────────────────────────────────────────
# 2. 事件 → 可访问性属性
# ──────────────────────────────────────────

EVENT_MAP = {
    "tap":      ("clickable",      "true"),
    "longtap":  ("long-clickable", "true"),
    "longpress":("long-clickable", "true"),
    "scroll":   ("scrollable",     "true"),
    "touchmove":("scrollable",     "true"),
    "focus":    ("focusable",      "true"),
    "focused":  ("focusable",      "true"),
}

def default_attrs(tag: str, resource_id: str | None) -> dict:
    return {
        "index": "0", "class": f"wx.{tag}", "resource-id": resource_id or "",
        "package": "", "content-desc": "",
        "checkable": "false",   "checked": "false",
        "clickable": "false",   "enabled": "true",
        "focusable": "false",   "focused": "false",
        "scrollable": "false",  "long-clickable": "false",
        "password": "false",    "selected": "false",
        "visible-to-user": "true", "bounds": "[0,0][0,0]",
        "drawing-order": "0",   "hint": "",
        "display-id": "0",      "text": "",
    }

# ──────────────────────────────────────────
# 3. 递归构建 <node> 树
# ──────────────────────────────────────────

def build_node(elem, global_state, xpath) -> ET.Element:        # global_state = {"idx": int}
    global_state["idx"] += 1
    idx_str = str(global_state["idx"])

    xpath += f"/{elem.tag}"
    print(elem.drop_tag)

    # 基础属性
    attrs = default_attrs(elem.tag, elem.attrib.get("id"))
    attrs["index"] = idx_str
    attrs["xpath"] = xpath

    # 原始属性全部复制
    for k, v in elem.attrib.items():
        attrs[k] = v

    # 映射事件到可操作性
    for e in re.findall(r"[\w-]+", elem.attrib.get("event", "")):
        if e in EVENT_MAP:
            k, v = EVENT_MAP[e]
            attrs[k] = v

    # 首段文本 → text
    if (txt := (elem.text or "").strip()):
        attrs["text"] = txt

    # 生成 <node>
    node_el = ET.Element("node", attrs)
    # 递归子元素
    for child in elem:
        node_el.append(build_node(child, global_state, xpath))
    return node_el

# ──────────────────────────────────────────
# 4. 顶层转换函数
# ──────────────────────────────────────────

def convert(raw_html: str) -> str:
    doc = html.fromstring(sanitize_html(raw_html))
    hierarchy = ET.Element("hierarchy", {"rotation": "0"})

    state = {"idx": 0}
    # lxml 解析结果可能有多个根节点；全部挂到 hierarchy 下
    for child in doc.iterchildren():
        hierarchy.append(build_node(child, state, "html"))

    ET.indent(hierarchy, space="  ")
    return ET.tostring(
        hierarchy, encoding="utf-8",
        xml_declaration=True, short_empty_elements=True
    ).decode()

# ──────────────────────────────────────────
# 5. CLI
# ──────────────────────────────────────────

if __name__ == "__main__":
    raw = Path(sys.argv[1]).read_text("utf-8") if len(sys.argv) > 1 else sys.stdin.read()
    out_xml = convert(raw)
    Path("hierarchy.xml").write_text(out_xml, "utf-8")
    print("✅  hierarchy.xml 生成成功 (lxml)。")
