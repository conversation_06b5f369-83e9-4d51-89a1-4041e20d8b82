<html>
  <body id="480" is="pages/index/main" event="['touchstart', 'touchmove']">
    <wx-view id="479" event="['touchend']">
      <wx-view>
        <wx-view id="15" event="['tap']" />
        <wx-cover-view id="31" event="['tap', 'longtap', 'longpress', 'touchstart', 'touchmove', 'touchend', 'touchcancel', 'animationstart', 'animationend']">
          <div[2]>
            <wx-cover-view id="19" event="['tap', 'longtap', 'longpress', 'touchstart', 'touchmove', 'touchend', 'touchcancel', 'animationstart', 'animationend']" />
            <wx-cover-view[2] id="29" event="['tap', 'longtap', 'longpress', 'touchstart', 'touchmove', 'touchend', 'touchcancel', 'animationstart', 'animationend']">
              <div[2]>
                <wx-cover-view id="23" event="['tap', 'longtap', 'longpress', 'touchstart', 'touchmove', 'touchend', 'touchcancel', 'animationstart', 'animationend']" />
                <wx-cover-view[2] id="27" event="['tap', 'longtap', 'longpress', 'touchstart', 'touchmove', 'touchend', 'touchcancel', 'animationstart', 'animationend']">
                  <div[2] id="26">微信读书</div[2]>
                </wx-cover-view[2]>
              </div[2]>
            </wx-cover-view[2]>
          </div[2]>
        </wx-cover-view>
      </wx-view>
      <wx-view[2]>
        <wx-view[2]>
          <wx-button id="35" class="_button data-v-4ba09dc0 index_search_bar" data-comkey="0" data-eventid="3_0" hover-class="index_search_bar_Active" role="button" aria-disabled="false" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">搜索</wx-button>
        </wx-view[2]>
        <wx-view[4]>
          <wx-view>
            <wx-view>
              <wx-view id="50" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">推荐</wx-view>
              <wx-view[2] id="52" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">分类</wx-view[2]>
              <wx-view[3] id="54" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">排行</wx-view[3]>
              <wx-view[4] id="56" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">文学艺术</wx-view[4]>
              <wx-view[5] id="58" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">哲学社科</wx-view[5]>
            </wx-view>
          </wx-view>
        </wx-view[4]>
        <wx-view[5]>
          <wx-swiper id="349" class="_swiper data-v-4ba09dc0 index_main_swiper" current="0" data-comkey="0" data-eventid="3_11" duration="300" easing-function="linear" interval="3000" skip-hidden-item-layout="" style=" height:1130px;" role="listbox" event="['wxPositioningTargetReady', 'animationfinish', 'change']">
            <div>
              <div>
                <div>
                  <wx-swiper-item id="327" event="['wxPositioningTargetReady']">
                    <wx-view>
                      <wx-view>
                        <wx-view id="99" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">
                          <wx-view id="79" event="['tap']" />
                          <wx-view[2]>
                            <wx-view id="81">有钱人和你想的不一样</wx-view>
                            <wx-view[2]>
                              <wx-text>
                                <span id="83">哈维·艾克</span>
                              </wx-text>
                              <wx-text[2]>
                                <span id="86">|</span>
                              </wx-text[2]>
                              <wx-view id="91">
                                <wx-view id="90">82.9%</wx-view>
                              </wx-view>
                            </wx-view[2]>
                            <wx-view[3] id="97">大家都在读</wx-view[3]>
                          </wx-view[2]>
                        </wx-view>
                        <wx-view[2] id="124" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">
                          <wx-view id="104" event="['tap']" />
                          <wx-view[2]>
                            <wx-view id="106">王阳明心学全书</wx-view>
                            <wx-view[2]>
                              <wx-text>
                                <span id="108">罗智</span>
                              </wx-text>
                              <wx-text[2]>
                                <span id="111">|</span>
                              </wx-text[2]>
                              <wx-view id="116">
                                <wx-view id="115">82.8%</wx-view>
                              </wx-view>
                            </wx-view[2]>
                            <wx-view[3] id="122">大家都在读</wx-view[3]>
                          </wx-view[2]>
                        </wx-view[2]>
                        <wx-view[3] id="149" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">
                          <wx-view id="129" event="['tap']" />
                          <wx-view[2]>
                            <wx-view id="131">白夜行</wx-view>
                            <wx-view[2]>
                              <wx-text>
                                <span id="133">东野圭吾</span>
                              </wx-text>
                              <wx-text[2]>
                                <span id="136">|</span>
                              </wx-text[2]>
                              <wx-view id="141">
                                <wx-view id="140">89.8%</wx-view>
                              </wx-view>
                            </wx-view[2]>
                            <wx-view[3] id="147">大家都在读</wx-view[3]>
                          </wx-view[2]>
                        </wx-view[3]>
                        <wx-view[4] id="174" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">
                          <wx-view id="154" event="['tap']" />
                          <wx-view[2]>
                            <wx-view id="156">法医秦明烧脑大合集（套装14册）</wx-view>
                            <wx-view[2]>
                              <wx-text>
                                <span id="158">法医秦明</span>
                              </wx-text>
                              <wx-text[2]>
                                <span id="161">|</span>
                              </wx-text[2]>
                              <wx-view id="166">
                                <wx-view id="165">85.7%</wx-view>
                              </wx-view>
                            </wx-view[2]>
                            <wx-view[3] id="172">大家都在读</wx-view[3]>
                          </wx-view[2]>
                        </wx-view[4]>
                        <wx-view[5] id="199" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">
                          <wx-view id="179" event="['tap']" />
                          <wx-view[2]>
                            <wx-view id="181">活着</wx-view>
                            <wx-view[2]>
                              <wx-text>
                                <span id="183">余华</span>
                              </wx-text>
                              <wx-text[2]>
                                <span id="186">|</span>
                              </wx-text[2]>
                              <wx-view id="191">
                                <wx-view id="190">92.0%</wx-view>
                              </wx-view>
                            </wx-view[2]>
                            <wx-view[3] id="197">大家都在读</wx-view[3]>
                          </wx-view[2]>
                        </wx-view[5]>
                        <wx-view[6] id="224" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">
                          <wx-view id="204" event="['tap']" />
                          <wx-view[2]>
                            <wx-view id="206">小王子</wx-view>
                          </wx-view[2]>
                        </wx-view[6]>
                      </wx-view>
                    </wx-view>
                  </wx-swiper-item>
                </div>
              </div>
            </div>
          </wx-swiper>
        </wx-view[5]>
      </wx-view[2]>
      <wx-view[4]>
        <wx-button id="390" class="_button data-v-4ba09dc0 pageTabBar_btn pageTabBar_btn_Active" data-comkey="0" data-eventid="2_18_0" hover-class="wr_commonActive" role="button" aria-disabled="false" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">
          <wx-view[2] id="389">书 城</wx-view[2]>
        </wx-button>
        <wx-button[2] id="394" class="_button data-v-4ba09dc0 pageTabBar_btn" data-comkey="0" data-eventid="2_18_1" hover-class="wr_commonActive" role="button" aria-disabled="false" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">
          <wx-view[2] id="393">听 书</wx-view[2]>
        </wx-button[2]>
      </wx-view[4]>
      <wx-view[5]>
        <wx-view[2]>
          <wx-view id="398">添加到我的小程序</wx-view>
          <wx-view[2] id="400">百万好书随时阅读</wx-view[2]>
        </wx-view[2]>
        <wx-view[3] id="402" aria-label="关闭添加小程序提示" aria-role="button" role="button" class="_view data-v-7399924c wr_pinTip_closeBtn" data-comkey="0_14" data-eventid="1_0" hover-class="wr_commonActive" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']" />
      </wx-view[5]>
      <wx-view[8]>
        <wx-view id="456" event="['animationend', 'tap', 'touchmove']" />
        <wx-view[2] id="477" event="['animationend', 'touchmove']">
          <wx-view>
            <wx-view id="458" event="['tap']" />
          </wx-view>
          <wx-view[2] id="461">微信读书隐私政策</wx-view[2]>
          <wx-view[3]>
            <wx-view id="463">欢迎你使用微信读书！请仔细阅读以下内容，并作出适当的选择:</wx-view>
            <wx-view[2]>
              <wx-view id="465">隐私政策概要</wx-view>
              <wx-view[2] id="467">主要说明：我们所处理的信息种类、方式和目的；你所享有的权益；第三方插件信息等。</wx-view[2]>
              <wx-view[3] id="469" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">点击阅读《隐私政策（小程序）》完整版 &gt;</wx-view[3]>
            </wx-view[2]>
          </wx-view[3]>
          <wx-view[4]>
            <wx-view id="473" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">同意并继续</wx-view>
            <wx-navigator id="475" class="_navigator data-v-41dbede8 privacy_agreement_button" hover-class="wr_commonActive" open-type="exit" target="miniProgram" role="link" style="cursor: pointer;" event="['tap', 'touchstart', 'canceltap', 'touchcancel', 'touchend']">不同意并退出</wx-navigator>
          </wx-view[4]>
        </wx-view[2]>
      </wx-view[8]>
    </wx-view>
  </body>
</html>
