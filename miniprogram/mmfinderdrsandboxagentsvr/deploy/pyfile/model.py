"""
定义脚本的输入输出类型
"""

import json
import traceback
from enum import StrEnum
from typing import Any

from opentelemetry import trace
from pydantic import BaseModel
from wxms.logger import logger
from wxms.model import SandboxActionStandardOutput
from wxms.service.clickhouse_service import Step, clickhouse_service
from wxms.service.sandbox_service import SandboxClient
from wxms.util.common_util import CustomJSONEncoder
from wxms.util.context_util import context_util
from wxms.util.time_util import time_util


class PythonInput(BaseModel):
    """
    脚本的输入类型
    """

    app_entry_mode: str
    app_entry_url: str
    app_id: str
    app_name: str
    base_url: str
    from_username: str
    headless_mode: str
    instruction: str
    model_name: str
    namespace: str
    prompt: str
    prompt_vlt: str
    prompt_vlt_v1: str
    rag_base_url: str
    raw_query: str
    run_mode: str
    sandbox_close_applet: str
    sandbox_disable_check_screenshot_loading_status: str
    sandbox_is_async_action_result: str
    sandbox_skip_launch_applet: str
    special_app_id_list: str
    special_str_1: str
    special_str_2: str
    uin: str
    username: str
    vlt_base_url: str
    vlt_base_url_v1: str
    vlt_model_name: str
    vlt_model_name_v1: str
    # 上下文信息，交由 TraceContextTextMapPropagator().extract(carrier) 解析
    # 必须保证 traceparent 有值
    traceparent: str
    tracestate: str


class PythonResultStatus(StrEnum):
    """
    脚本输出的参数 status 枚举
    """

    FAILED = "failed"
    SUCCESS = "success"


class PythonResult(BaseModel):
    """
    脚本的输出类型
    """

    # 各个 agent 自定义的返回
    answers: list[str]
    # inference 阶段大模型输出的 raw data，数量应该是推理步骤的整数倍
    answers_raw_data: list[str]
    # debug 的 log 数据
    log_data: list[dict[str, Any]]
    # 中断信息，为空则不是中断退出
    interrupt: dict
    # 文本模型才添加：dom xml 的简化，作为大模型输入的一部分能更好地反映关键信息，和 standard_output_list 一一对应
    screens: list[str]
    # 标准操作的输出列表
    standard_output_list: list[SandboxActionStandardOutput]
    # 脚本执行的状态
    status: PythonResultStatus
    # 小程序页面的 target_id
    target_id: str


class SandboxAgentBaseMeta(type):
    """
    沙箱 Agent 推理类的元类，自动装饰一些方法
    """

    def __new__(mcs, name, bases, namespace):
        if "init_step" in namespace:
            namespace["init_step"] = context_util.add_trace_span()(
                namespace["init_step"]
            )
        if "run_one_step" in namespace:
            namespace["run_one_step"] = context_util.add_trace_span()(
                namespace["run_one_step"]
            )
        return super().__new__(mcs, name, bases, namespace)


class SandboxAgentBase(metaclass=SandboxAgentBaseMeta):
    """
    沙箱 Agent 推理类
    """

    def __init__(
        self,
        python_input: PythonInput,
        python_result: PythonResult,
        sandbox_client: SandboxClient,
    ):
        self.python_input = python_input
        self.python_result = python_result
        self.sandbox_client = sandbox_client
        self.max_step = 50
        self.step_count = -1
        self.last_trace_answer_index = -1
        self.last_trace_answer_raw_data_index = -1
        self.last_trace_log_data_index = -1
        self.last_trace_screen_index = -1

    def init_step(self):
        """
        初始化步骤，大部分代码其实都应该放在 __init__ 中
        但如果为了 instrumentation，可以将一些 trace 上报放在这里
        """
        started_at = time_util.get_millisecond_timestamp_of_current_time()
        context_util.set_data_into_context(
            "sandbox_step_id", context_util.get_span_id()
        )
        error_dict = {}
        try:
            self.init_step_internal()
        except Exception as e:
            error_dict = {
                "exception": str(e),
                "traceback": traceback.format_exc(),
            }
            logger.error(
                "init_step failed",
                extra={
                    "customized_data_info": error_dict,
                },
            )
            raise e
        finally:
            # 给所在 span 添加信息
            clickhouse_service.add_step(
                Step(
                    started_at=started_at,
                    ended_at=time_util.get_millisecond_timestamp_of_current_time(),
                    session_id=context_util.get_trace_id(),
                    sub_session_id=context_util.get_data_from_context(
                        "sandbox_sub_session_id"
                    ),
                    step_id=context_util.get_data_from_context("sandbox_step_id"),
                    answer=json.dumps(
                        self.trace_add_answer_event(), cls=CustomJSONEncoder
                    ),
                    answer_raw_data=json.dumps(
                        self.trace_add_answer_raw_data_event(), cls=CustomJSONEncoder
                    ),
                    log_data=json.dumps(
                        self.trace_add_log_data_event(), cls=CustomJSONEncoder
                    ),
                    screen=json.dumps(
                        self.trace_add_screen_event(), cls=CustomJSONEncoder
                    ),
                    result=json.dumps(error_dict, cls=CustomJSONEncoder),
                )
            )
            context_util.set_data_into_context("sandbox_step_id", "")

    def init_step_internal(self):
        """
        初始化步骤，让子类重写的方法
        """

    def run(self):
        """
        沙箱推理
        """
        for step_count in range(self.max_step):
            self.step_count = step_count
            if self.run_one_step():
                break

    def run_one_step(self) -> bool:
        """
        沙箱单步推理

        :return: 是否推理结束
        """
        started_at = time_util.get_millisecond_timestamp_of_current_time()
        context_util.set_data_into_context(
            "sandbox_step_id", context_util.get_span_id()
        )
        error_dict = {}
        try:
            res = self.run_one_step_internal()
        except Exception as e:
            error_dict = {
                "exception": str(e),
                "traceback": traceback.format_exc(),
            }
            logger.error(
                "run_one_step failed",
                extra={
                    "customized_data_info": error_dict,
                },
            )
            raise e
        finally:
            # 给所在 span 添加信息
            clickhouse_service.add_step(
                Step(
                    started_at=started_at,
                    ended_at=time_util.get_millisecond_timestamp_of_current_time(),
                    session_id=context_util.get_trace_id(),
                    sub_session_id=context_util.get_data_from_context(
                        "sandbox_sub_session_id"
                    ),
                    step_id=context_util.get_data_from_context("sandbox_step_id"),
                    answer=json.dumps(
                        self.trace_add_answer_event(), cls=CustomJSONEncoder
                    ),
                    answer_raw_data=json.dumps(
                        self.trace_add_answer_raw_data_event(), cls=CustomJSONEncoder
                    ),
                    log_data=json.dumps(
                        self.trace_add_log_data_event(), cls=CustomJSONEncoder
                    ),
                    screen=json.dumps(
                        self.trace_add_screen_event(), cls=CustomJSONEncoder
                    ),
                    result=json.dumps(error_dict, cls=CustomJSONEncoder),
                )
            )
            context_util.set_data_into_context("sandbox_step_id", "")
        return res

    def run_one_step_internal(self) -> bool:
        """
        沙箱单步推理，让子类重写的方法

        :return: 是否推理结束
        """
        return False

    def trace_add_answer_event(self) -> list[str]:
        """
        给所在 span 添加 answer 信息
        这个信息的产生由各自业务决定
        但强烈建议这个信息的产生在 observation 之后，且一个 run_one_step 中只有一个 answer
        """
        data = self.python_result.answers[self.last_trace_answer_index + 1 :]
        self.last_trace_answer_index = len(self.python_result.answers) - 1
        current_span = trace.get_current_span()
        for i in data:
            current_span.add_event(
                name="answer",
                attributes={
                    "name": "answer",
                    "data": i,
                },
            )
        return data

    def trace_add_answer_raw_data_event(self) -> list[str]:
        """
        给所在 span 添加 answer_raw_data 信息（inference 阶段大模型输出的 raw data）
        这个信息的产生介于 inference 和 action 之间
        由于 run_one_step 的一次执行中可能有多次模型调用，所以这里 data 是一个 list
        """
        data = self.python_result.answers_raw_data[
            self.last_trace_answer_raw_data_index + 1 :
        ]
        self.last_trace_answer_raw_data_index = (
            len(self.python_result.answers_raw_data) - 1
        )
        current_span = trace.get_current_span()
        for i in data:
            current_span.add_event(
                name="answer_raw_data",
                attributes={
                    "name": "answer_raw_data",
                    "data": i,
                },
            )
        return data

    def trace_add_log_data_event(self) -> list[dict[str, Any]]:
        """
        给所在 span 添加 log_data 信息
        这个信息的产生介于 inference 和 action 之间（为了方便定位问题）
        由于 run_one_step 的一次执行中可能会执行多个 action，所以这里 data 是一个 list
        """
        data = self.python_result.log_data[self.last_trace_log_data_index + 1 :]
        self.last_trace_log_data_index = len(self.python_result.log_data) - 1
        current_span = trace.get_current_span()
        for i in data:
            current_span.add_event(
                name="log_data",
                attributes={
                    "name": "log_data",
                    "data": json.dumps(i, cls=CustomJSONEncoder),
                },
            )
        return data

    def trace_add_screen_event(self) -> list[str]:
        """
        给所在 span 添加 screen_list 信息，表示 dom xml 的简化，作为大模型输入的一部分能更好地反映关键信息
        这个信息的产生在 observation 之后
        由于 run_one_step 的一次执行中可能会执行多个 action，所以这里 data 是一个 list
        """
        data = self.python_result.screens[self.last_trace_screen_index + 1 :]
        self.last_trace_screen_index = len(self.python_result.screens) - 1
        current_span = trace.get_current_span()
        for i in data:
            current_span.add_event(
                name="screen",
                attributes={"name": "screen", "data": i},
            )
        return data
