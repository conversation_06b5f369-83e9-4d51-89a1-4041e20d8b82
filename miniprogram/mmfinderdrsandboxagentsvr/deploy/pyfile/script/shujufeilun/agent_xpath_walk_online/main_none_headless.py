import json
import os
import random
import time
import traceback
from concurrent.futures import ThreadPoolExecutor

import requests

AUTH_CODE_API_URL = (
    "http://mmfinderdrsandboxfdsvr.production.polaris/v1/sandbox/auth_code"
)
AGENT_API_URL = "http://mmfinderdrsandboxfdsvr.production.polaris/v1/agent/run"
NUM_THREADS = 5
TOTAL_COUNT = 5
ACCOUNT_LIST = []
with open(os.path.dirname(__file__) + "/data.txt", "r", encoding="utf-8") as f:
    DATA_LIST = f.readlines()


def generate_workflow_run_id() -> str:
    res = "".join(random.sample("0123456789abcdef", 8))
    res += "".join(random.sample("0123456789abcdef", 4))
    res += "".join(random.sample("0123456789abcdef", 4))
    res += "".join(random.sample("0123456789abcdef", 4))
    res += "".join(random.sample("0123456789abcdef", 12))
    return res


def run_data(index: int):
    account = ACCOUNT_LIST[index % len(ACCOUNT_LIST)]
    data = DATA_LIST[index % len(DATA_LIST)]
    data_dict = json.loads(data)
    app_id = data_dict.get("app_id", "")
    xpaths = data_dict.get("xpaths", "")
    paths = data_dict.get("paths", "")
    res = ""
    workflow_run_id = generate_workflow_run_id()
    try:
        response = requests.get(AUTH_CODE_API_URL + "?uin=" + account)
        temp_data = response.json()
        auth_code = temp_data["data"]["ilink_auth_code"]
        req = {
            "app_id": app_id,
            "run_mode": "agent_xpath_walk_online",
            "special_str_1": xpaths,
            "special_str_2": paths,
            "skip_share_url_data": True,
            "auth_code": auth_code,
        }
        headers = {"x-workflow-run-id": workflow_run_id}
        response = requests.post(AGENT_API_URL, json=req, headers=headers, timeout=1200)
        temp_data = response.json()
        res = json.dumps(temp_data)
    except Exception as e:
        res = str(e) + "\n" + traceback.format_exc()

    timestamp = time.strftime("%Y%m%d_%H%M%S_%f")[:-3]
    random_id = "".join(random.sample("0123456789abcdef", 8))
    folder = f"{os.path.dirname(__file__)}/{app_id}"
    os.makedirs(folder, exist_ok=True)
    file_name = f"{folder}/{timestamp}_{random_id}_{workflow_run_id}"
    with open(file_name, "w", encoding="utf-8") as f:
        print(res)
        f.write(res)


# 使用线程池并发执行
def run_concurrent_requests():
    pool = ThreadPoolExecutor(max_workers=NUM_THREADS)
    count = 0
    task_list = []
    while True:
        for _ in range(NUM_THREADS):
            task_list.append(pool.submit(run_data, count))
            count += 1
            time.sleep(1)
        for task in task_list:
            task.result()
        if count >= TOTAL_COUNT:
            break


if __name__ == "__main__":
    # nohup python main.py > main.log 2>&1 &
    run_concurrent_requests()
