import json
import os
import random
import time
import traceback
from concurrent.futures import ThreadPoolExecutor
from functools import wraps
from typing import Callable, Tuple, Type

import requests

AGENT_API_URL = (
    "http://mmfinderdrsandboxagentsvr.production.polaris/v1/agent/run_headless"
)
NUM_THREADS = 1
TOTAL_COUNT = 1000
ACCOUNT_LIST = [
    "**********",  # "**********", # "**********",
]
with open(os.path.dirname(__file__) + "/appid.txt", "r", encoding="utf-8") as f:
    APP_LIST = [line.strip() for line in f.readlines() if line.strip() != ""]


def generate_workflow_run_id() -> str:
    res = "".join(random.sample("0123456789abcdef", 8))
    res += "".join(random.sample("0123456789abcdef", 4))
    res += "".join(random.sample("0123456789abcdef", 4))
    res += "".join(random.sample("0123456789abcdef", 4))
    res += "".join(random.sample("0123456789abcdef", 12))
    return res


def retry(
    exceptions: Tuple[Type[Exception]] = (Exception,),
    max_retries: int = 3,
    initial_delay: float = 1.0,
    backoff_factor: float = 1.5,
    max_delay: float = 30.0,
):
    """
    异常重试

    参数:
        exceptions: 要捕获的异常类型
        max_retries: 最大重试次数（包括首次尝试）
        initial_delay: 初始延迟时间（秒）
        backoff_factor: 延迟倍数（指数退避因子）
        max_delay: 最大延迟时间（秒）
    """

    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            delay = initial_delay

            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    retries += 1
                    if retries >= max_retries:
                        print(f"操作失败，已达最大重试次数 ({max_retries})")
                        raise  # 重新抛出异常

                    # 指数退避计算
                    sleep_time = min(delay, max_delay)
                    print(
                        f"操作失败: {e}. 将在 {sleep_time:.1f}s 后重试 ({retries}/{max_retries})"
                    )
                    time.sleep(sleep_time)

                    # 更新下次延迟时间
                    delay *= backoff_factor

        return wrapper

    return decorator


@retry(exceptions=(TimeoutError), max_retries=4, initial_delay=120, max_delay=300)
def check_preinstall_success(device_id: int) -> bool:
    device = get_device_by_id(device_id)
    ok = device.get("preinstall_success", False)
    status = device.get("preinstall_status", "")
    report_url = device.get("preinstall_report_url", "")
    if ok:
        return ok
    print(
        f"device {device_id} preinstall failed, status: {status}, report_url: {report_url}"
    )
    if status == "failed":
        return False
    elif status == "running":
        raise TimeoutError("waiting for preinstall")
    raise RuntimeError("unknown error")


default_script_config = {
    "simulate_location": {"latitude": 39.906217, "longitude": 116.3912758}
}


def preinstall_app(
    device_id: int,
    build_tag: str,
    cloud_id: int = 638,
    script_config: dict = default_script_config,
):
    headers = {
        "X-App-Id": "1",
        "Content-Type": "application/json",
        "Cookie": "x-client-ssid=6516b00b:01971b6045c4:018fc9; x_host_key_access=194d058c37ec09236797d9399d65caae72ad095d_s",
    }
    body = {
        "args": None,
        "kwargs": {
            "build_tag": build_tag,
            "device_id": device_id,
            "cloud_id": cloud_id,
            "script_config": script_config,
        },
        "logic": "MiniAppAiLogic",
        "method": "preinstall_app",
    }
    try:
        resp = requests.post(
            "http://mt.woa.com/epuiat/external/miniapp_ai", json=body, headers=headers
        )
        print(resp.text)
        return True
    except Exception as e:
        print(e)
        return False


def login_device(device_id: int, username: str, password: str) -> bool:
    headers = {
        "X-App-Id": "1",
        "Content-Type": "application/json",
        "Cookie": "x-client-ssid=6516b00b:01971b6045c4:018fc9; x_host_key_access=194d058c37ec09236797d9399d65caae72ad095d_s",
    }
    body = {
        "args": None,
        "kwargs": {
            "device_id": device_id,
            "username": username,
            "password": password,
            "script_config": {
                "simulate_location": {"latitude": 39.906217, "longitude": 116.3912758}
            },
        },
        "logic": "MiniAppAiLogic",
        "method": "login_device",
    }
    try:
        resp = requests.post(
            "http://mt.woa.com/epuiat/external/miniapp_ai", json=body, headers=headers
        )
        print(resp.text)
        return True
    except Exception as e:
        print(e)
        return False


def get_cloud_device_infos() -> list:
    headers = {
        "X-App-Id": "1",
        "Content-Type": "application/json",
        "Cookie": "x-client-ssid=6516b00b:01971b6045c4:018fc9; x_host_key_access=194d058c37ec09236797d9399d65caae72ad095d_s",
    }
    body = {
        "args": None,
        "kwargs": {"cloud_id": 638},
        "logic": "MiniAppAiLogic",
        "method": "get_cloud_device_infos",
    }
    try:
        resp = requests.post(
            "http://mt.woa.com/epuiat/external/miniapp_ai", json=body, headers=headers
        )
        body = resp.json()
        device_infos = body.get("device_infos", [])
        return device_infos
    except Exception as e:
        print(e)


def get_device_by_id(device_id: int) -> dict:
    device_info = [
        device
        for device in get_cloud_device_infos()
        if device.get("device_id", 0) == device_id
    ]
    if len(device_info) == 0:
        raise ValueError(f"device {device_id} not found")
    return device_info[0]


@retry(exceptions=(TimeoutError), max_retries=6, initial_delay=20, max_delay=60)
def check_login_success(device_id: int) -> bool:
    # todo: find unused user
    device = get_device_by_id(device_id)
    ok = device.get("login_success", False)
    status = device.get("login_status", "")
    report_url = device.get("login_report_url", "")
    if ok:
        return ok
    print(
        f"device {device_id} login failed, status: {status}, report_url: {report_url}"
    )
    if status == "failed":
        return False
    elif status == "running":
        raise TimeoutError("waiting for login")
    raise RuntimeError("unknown error")


def destroy_device(device_id: int) -> bool:
    headers = {
        "X-App-Id": "1",
        "Content-Type": "application/json",
        "Cookie": "x-client-ssid=6516b00b:01971b6045c4:018fc9; x_host_key_access=194d058c37ec09236797d9399d65caae72ad095d_s",
    }
    body = {
        "args": None,
        "kwargs": {
            "device_id": device_id,
        },
        "logic": "MiniAppAiLogic",
        "method": "destroy_device",
    }
    try:
        resp = requests.post(
            "http://mt.woa.com/epuiat/external/miniapp_ai", json=body, headers=headers
        )
        print(resp.text)
        return True
    except Exception as e:
        print(e)
        return False


@retry(exceptions=(TimeoutError), max_retries=6, initial_delay=20, max_delay=60)
def check_logout(device_id: int) -> bool:
    device = get_device_by_id(device_id)
    ok = device.get("login_success", False)
    status = device.get("login_status", "")
    report_url = device.get("login_report_url", "")
    if not ok:
        return True
    print(
        f"device {device_id} logout failed, status: {status}, report_url: {report_url}"
    )
    if status == "failed":
        return True
    elif status == "running":
        raise TimeoutError("waiting for logout")
    raise RuntimeError("unknown error")


def run_data(index: int):
    account = ACCOUNT_LIST[index % len(ACCOUNT_LIST)]
    app_id = random.choice(APP_LIST).strip()
    if app_id == "":
        return False
    res = ""
    workflow_run_id = generate_workflow_run_id()

    device_id = 6355  # 2250 # 3797 # 4983
    device = get_device_by_id(device_id)

    try:
        if not device.get("preinstall_success", False):
            if not check_preinstall_success(device_id):
                raise RuntimeError("preinstall app failed")
        if not device.get("login_success", False):
            if not login_device(device_id, "wxid_xnvxg0q8nkj712", "xiaochenxu123_"):
                # if not login_device(device_id, "wxid_veo3h8a2nj7f12", "xiaochenxu123_"):
                raise RuntimeError("login device failed")
            if not check_login_success(device_id):
                raise RuntimeError("login device failed")
    except Exception as e:
        print(time.time(), e)
        raise e

    try:
        req = {
            "app_id": app_id,
            "run_mode": "agent_random_walk_online",
            "uin": account,
            "request_id": workflow_run_id,
            "username": "test_",
            "from_username": "test_",
            "headless_mode": "2",
        }
        headers = {"x-workflow-run-id": workflow_run_id}
        response = requests.post(AGENT_API_URL, json=req, headers=headers, timeout=300)
        temp_data = response.json()
        res = json.dumps(temp_data, ensure_ascii=False)
    except Exception as e:
        res = str(e) + "\n" + traceback.format_exc()
        print(time.time(), res)
        try:
            if device.get("login_success", False):
                destroy_device(device_id)
                check_logout(device_id)
        #     preinstall_app(device_id, "br_for_weops2 #14127")
        #     check_preinstall_success(device_id)
        except Exception as e:
            print(e)
        return False

    timestamp = time.strftime("%Y%m%d_%H%M%S_%f")[:-3]
    random_id = "".join(random.sample("0123456789abcdef", 8))
    folder = f"outputs/{app_id}"
    os.makedirs(folder, exist_ok=True)
    file_name = f"{folder}/{timestamp}_{random_id}_{workflow_run_id}"
    with open(file_name, "w", encoding="utf-8") as f:
        print(res)
        f.write(res)
    return True


# 使用线程池并发执行
def run_concurrent_requests():
    with ThreadPoolExecutor(max_workers=NUM_THREADS) as pool:
        count = 0
        task_list = list()
        while True:
            for _ in range(NUM_THREADS):
                task_list.append(pool.submit(run_data, count))
            for task in task_list:
                if task.result():
                    count += 1
            task_list = list()
            if count >= TOTAL_COUNT:
                break
            time.sleep(1)


if __name__ == "__main__":
    # nohup python shujufeilun.py > shujufeilun.log 2>&1 &
    run_concurrent_requests()
