import base64
import json
import math
import os
import re
import subprocess
import sys
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

import cv2
import openai
from openpyxl import Workbook
from openpyxl.drawing.image import Image
from openpyxl.utils import get_column_letter
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
from tabulate import tabulate
from tqdm import tqdm


def sim_calc_by_bert(sentences):
    # 1. 加载预训练模型
    model = SentenceTransformer("/data/workspace/work/mini/bert/model/all-MiniLM-L6-v2")
    # 2. 计算句子嵌入向量
    embeddings = model.encode(sentences)
    # 3. 计算相似度矩阵 (使用余弦相似度)
    similarity_matrix = cosine_similarity(embeddings)
    return similarity_matrix[0][1]


class EvalInferV2:
    def __init__(self, task_name, vl_prompt, base_url, model_name, max_workers=4):
        self.task_name = task_name
        self.vl_prompt = vl_prompt
        self.base_url = base_url
        self.model_name = model_name
        self.max_workers = max_workers
        # tm: 模糊匹配，click，search
        # em: 精确匹配，click，search
        # 总的准确率
        self.tm_ok = 0
        self.em_ok = 0
        self.total_step = 0
        # 每种类型的准确率
        # 点击
        self.tm_click_ok = 0
        self.em_click_ok = 0
        self.pred_click = 0
        self.click_total_step = 0
        # 搜索
        self.tm_search_ok = 0
        self.em_search_ok = 0
        self.pred_search = 0
        self.search_total_step = 0
        # 下滑
        self.scroll_ok = 0
        self.pred_scroll = 0
        self.scroll_total_step = 0
        # 等待
        self.wait_ok = 0
        self.pred_wait = 0
        self.wait_total_step = 0
        # 结束
        self.finsh_ok = 0
        self.pred_finsh = 0
        self.finish_total_step = 0
        # 错误
        self.pred_error = 0
        # pixel dist
        self.pixel_dist = 0
        self.pixel_dist_cnt = 0
        # 时间统计
        self.total_infer_time = 0
        self.total_samples = 0
        self.total_steps = 0

    def _image_to_base64(self, image_path):
        try:
            with open(image_path, "rb") as image_file:
                encoded_string = base64.b64encode(image_file.read())
                return encoded_string.decode("utf-8")
        except FileNotFoundError:
            print(f"错误：未找到指定的图片文件: {image_path}", file=sys.stderr)
        except Exception as e:
            print(f"错误：发生了未知错误: {str(e)}", file=sys.stderr)
            print(f"错误图片路径: {image_path}", file=sys.stderr)

    def _Qwen25VL7BInstruct(self, pre_imgs, instruction):
        try:
            client = openai.OpenAI(
                api_key="EMPTY",
                base_url=self.base_url,
            )
            reasoning_content = ""  # 定义完整思考过程
            answer_content = ""  # 定义完整回复
            is_answering = False  # 判断是否结束思考过程并开始回复
            input_imgs = pre_imgs[-1:]

            input_imgs_content = [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{self._image_to_base64(img)}"
                    },
                }
                for img in input_imgs
            ]
            messages = [
                {
                    "role": "user",
                    "content": input_imgs_content
                    + [{"type": "text", "text": self.vl_prompt.format(instruction)}],
                }
            ]
            # print('vl: ', len(messages[0]['content']))
            completion = client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                temperature=0.0,
                seed=2025,
                stream=True,
            )
            for chunk in completion:
                # 处理usage信息
                if not getattr(chunk, "choices", None):
                    print("\n" + "=" * 20 + "Token 使用情况" + "=" * 20 + "\n")
                    print(chunk.usage)
                    continue

                delta = chunk.choices[0].delta

                # 处理空内容情况
                if not getattr(delta, "reasoning_content", None) and not getattr(
                    delta, "content", None
                ):
                    continue

                # 处理开始回答的情况
                if not getattr(delta, "reasoning_content", None) and not is_answering:
                    # print("\n" + "=" * 20 + "完整回复" + "=" * 20 + "\n")
                    is_answering = True

                # 处理思考过程
                if getattr(delta, "reasoning_content", None):
                    # print(delta.reasoning_content, end='', flush=True)
                    reasoning_content += delta.reasoning_content
                # 处理回复内容
                elif getattr(delta, "content", None):
                    # print(delta.content, end='', flush=True)
                    answer_content += delta.content
            return answer_content

        except Exception as e:
            print(e)
            return ""

    def _infer_singel(self, pre_imgs, instruction):
        answer_content = self._Qwen25VL7BInstruct(pre_imgs, instruction)
        match = re.search(r"<answer>(.*?)</answer>", answer_content, re.DOTALL)
        if match:
            action_7b = match.group(1)  # 提取第一个捕获组中的内容
        else:
            action_7b = ""
        match_think = re.search(r"<think>(.*?)</think>", answer_content, re.DOTALL)
        if match_think:
            match_think.group(1)
        else:
            pass
        action_7b = action_7b.replace("\n", "")
        ret = {"type": "", "x": 0.0, "y": 0.0, "text": ""}
        try:
            if action_7b.startswith("click"):
                parts = action_7b.replace(",", " ").split()
                x = int(parts[1])
                y = int(parts[2])
                # return 'click', x, y
                ret["type"] = "click"
                ret["x"] = x
                ret["y"] = y
            elif action_7b.startswith("input"):
                parts = action_7b.replace(",", " ").split()
                x = int(parts[1])
                y = int(parts[2])
                text = parts[3]
                ret["type"] = "input"
                ret["x"] = x
                ret["y"] = y
                ret["text"] = text
            elif action_7b.startswith("scroll"):
                x = int(action_7b.split(" ", 1)[1])
                ret["type"] = "scroll"
                ret["x"] = x
            elif action_7b == "finish" or action_7b == "stop":
                ret["type"] = "finish"
            elif action_7b.startswith("wait"):
                ret["type"] = "wait"
            else:
                ret["type"] = "error"
        except Exception as e:
            print(e)
            ret["type"] = "error"
        return ret

    def _download_image_with_curl(self, url, output_path):
        try:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            command = ["curl", "-o", output_path, url]
            result = subprocess.run(command, check=True, capture_output=True, text=True)
            if result.returncode == 0:
                return True
            else:
                return False
        except subprocess.CalledProcessError as e:
            print(f"curl 执行错误: {e.stderr}")
            return False
        except Exception as e:
            print(f"未知错误: {e}")
            return False

    def print_metrics(self):
        # Overall metrics
        overall_metrics = [
            [
                "Total Match (TM)",
                f"{self.tm_ok}/{self.total_step}",
                (
                    f"{self.tm_ok/self.total_step*100:.2f}%"
                    if self.total_step > 0
                    else "0%"
                ),
            ],
            [
                "Exact Match (EM)",
                f"{self.em_ok}/{self.total_step}",
                (
                    f"{self.em_ok/self.total_step*100:.2f}%"
                    if self.total_step > 0
                    else "0%"
                ),
            ],
        ]

        # Action type metrics
        action_metrics = [
            [
                "Click",
                f"{self.tm_click_ok}/{self.click_total_step}",
                (
                    f"{self.tm_click_ok/self.click_total_step*100:.2f}%"
                    if self.click_total_step > 0
                    else "0%"
                ),
                f"{self.em_click_ok}/{self.click_total_step}",
                (
                    f"{self.em_click_ok/self.click_total_step*100:.2f}%"
                    if self.click_total_step > 0
                    else "0%"
                ),
                f"{self.pred_click}",
            ],
            [
                "Search",
                f"{self.tm_search_ok}/{self.search_total_step}",
                (
                    f"{self.tm_search_ok/self.search_total_step*100:.2f}%"
                    if self.search_total_step > 0
                    else "0%"
                ),
                f"{self.em_search_ok}/{self.search_total_step}",
                (
                    f"{self.em_search_ok/self.search_total_step*100:.2f}%"
                    if self.search_total_step > 0
                    else "0%"
                ),
                f"{self.pred_search}",
            ],
            [
                "Scroll",
                f"{self.scroll_ok}/{self.scroll_total_step}",
                (
                    f"{self.scroll_ok/self.scroll_total_step*100:.2f}%"
                    if self.scroll_total_step > 0
                    else "0%"
                ),
                "-",
                "-",
                f"{self.pred_scroll}",
            ],
            [
                "Wait",
                f"{self.wait_ok}/{self.wait_total_step}",
                (
                    f"{self.wait_ok/self.wait_total_step*100:.2f}%"
                    if self.wait_total_step > 0
                    else "0%"
                ),
                "-",
                "-",
                f"{self.pred_wait}",
            ],
            [
                "Finish",
                f"{self.finsh_ok}/{self.finish_total_step}",
                (
                    f"{self.finsh_ok/self.finish_total_step*100:.2f}%"
                    if self.finish_total_step > 0
                    else "0%"
                ),
                "-",
                "-",
                f"{self.pred_finsh}",
            ],
        ]

        # Pixel distance metrics
        pixel_metrics = [
            [
                "Average Pixel Distance",
                (
                    f"{self.pixel_dist/self.pixel_dist_cnt:.2f}"
                    if self.pixel_dist_cnt > 0
                    else "0"
                ),
                f"Total samples: {self.pixel_dist_cnt}",
            ]
        ]

        print("\n=== Overall Metrics ===")
        print(
            tabulate(
                overall_metrics,
                headers=["Metric", "Score", "Percentage"],
                tablefmt="grid",
            )
        )

        print("\n=== Action Type Metrics ===")
        print(
            tabulate(
                action_metrics,
                headers=[
                    "Action",
                    "TM Score",
                    "TM %",
                    "EM Score",
                    "EM %",
                    "Predictions",
                ],
                tablefmt="grid",
            )
        )

        print("\n=== Pixel Distance Metrics ===")
        print(
            tabulate(
                pixel_metrics, headers=["Metric", "Value", "Info"], tablefmt="grid"
            )
        )

    def _process_single_step(self, pre_imgs, instruction, gt_action):
        """Process a single inference step and update metrics"""
        start_time = time.time()
        pred_action = self._infer_singel(pre_imgs, instruction)
        infer_time = time.time() - start_time

        pred_type = pred_action["type"]
        gt_type = gt_action["type"]

        # 计算匹配状态
        status = 0  # 默认完全不匹配
        if pred_type == gt_type:
            if pred_type in ["click", "input"]:
                # 检查坐标是否在目标区域内
                if (
                    pred_action["x"] >= gt_action["x"]
                    and pred_action["x"] <= gt_action["rx"]
                    and pred_action["y"] >= gt_action["y"]
                    and pred_action["y"] <= gt_action["ry"]
                ):
                    if pred_type == "input":
                        # 对于input类型，还需要检查文本匹配
                        pred_text = pred_action.get("text", "")
                        gt_text = gt_action.get("text", "")
                        if pred_text in gt_text or gt_text in pred_text:
                            status = 2
                        else:
                            sim_score = sim_calc_by_bert([pred_text, gt_text])
                            if sim_score > 0.8:
                                status = 2
                            else:
                                status = 1
                    else:
                        status = 2  # click类型，坐标匹配即为完全匹配
                else:
                    status = 1  # 类型匹配但坐标不在区域内为部分匹配
            else:
                # 对于scroll, wait, finish等类型，类型匹配即为完全匹配
                status = 2

        results = {
            "total_step": 1,
            "click_total_step": 0,
            "search_total_step": 0,
            "scroll_total_step": 0,
            "wait_total_step": 0,
            "finish_total_step": 0,
            "pred_click": 0,
            "pred_search": 0,
            "pred_scroll": 0,
            "pred_wait": 0,
            "pred_finsh": 0,
            "pred_error": 0,
            "pixel_dist": 0.0,
            "pixel_dist_cnt": 0,
            "tm_ok": 0,
            "em_ok": 0,
            "tm_click_ok": 0,
            "em_click_ok": 0,
            "tm_search_ok": 0,
            "em_search_ok": 0,
            "scroll_ok": 0,
            "wait_ok": 0,
            "finsh_ok": 0,
            "infer_time": infer_time,
            "status": status,
            "pred_action": pred_action,
            "gt_action": gt_action,
            "last_img": pre_imgs[-1] if pre_imgs else None,
        }

        if results["last_img"] and os.path.exists(results["last_img"]):
            img = cv2.imread(results["last_img"])

            # 绘制红色框(gt_action)
            if (
                "x" in gt_action
                and "y" in gt_action
                and "rx" in gt_action
                and "ry" in gt_action
            ):
                x1, y1 = int(gt_action["x"]), int(gt_action["y"])
                x2, y2 = int(gt_action["rx"]), int(gt_action["ry"])
                cv2.rectangle(img, (x1, y1), (x2, y2), (0, 0, 255), 2)  # 红色细框

            # 绘制蓝色点(pred_action)
            if pred_action["type"] in ["click", "input"]:
                x, y = int(pred_action["x"]), int(pred_action["y"])
                cv2.circle(img, (x, y), 10, (255, 0, 0), -1)  # 蓝色实心圆

            # 保存修改后的图片
            cv2.imwrite(results["last_img"], img)

        # 计算真实类型总数
        if gt_type == "click":
            results["click_total_step"] = 1
        elif gt_type == "input":
            results["search_total_step"] = 1
        elif gt_type == "scroll":
            results["scroll_total_step"] = 1
        elif gt_type == "wait":
            results["wait_total_step"] = 1
        elif gt_type == "finish":
            results["finish_total_step"] = 1

        # 计算预测类型总数
        if pred_type == "click":
            results["pred_click"] = 1
        elif pred_type == "input":
            results["pred_search"] = 1
        elif pred_type == "scroll":
            results["pred_scroll"] = 1
        elif pred_type == "wait":
            results["pred_wait"] = 1
        elif pred_type == "finish":
            results["pred_finsh"] = 1
        else:
            results["pred_error"] = 1

        if pred_type == gt_type:
            results["tm_ok"] = 1
            if pred_type == "click":
                results["tm_click_ok"] = 1
                # em 匹配
                results["pixel_dist_cnt"] = 1
                # 距离中心点点欧式距离
                results["pixel_dist"] = math.sqrt(
                    (pred_action["x"] - gt_action["cx"]) ** 2
                    + (pred_action["y"] - gt_action["cy"]) ** 2
                )
                if (
                    pred_action["x"] >= gt_action["x"]
                    and pred_action["x"] <= gt_action["rx"]
                    and pred_action["y"] >= gt_action["y"]
                    and pred_action["y"] <= gt_action["ry"]
                ):
                    results["em_click_ok"] = 1
                    results["em_ok"] = 1
            elif pred_type == "input":
                results["tm_search_ok"] = 1
                # em 匹配
                results["pixel_dist_cnt"] = 1
                # 距离中心点点欧式距离
                results["pixel_dist"] = math.sqrt(
                    (pred_action["x"] - gt_action["cx"]) ** 2
                    + (pred_action["y"] - gt_action["cy"]) ** 2
                )
                if (
                    pred_action["x"] >= gt_action["x"]
                    and pred_action["x"] <= gt_action["rx"]
                    and pred_action["y"] >= gt_action["y"]
                    and pred_action["y"] <= gt_action["ry"]
                ):
                    pred_text = pred_action["text"]
                    gt_text = gt_action["text"]
                    em_flag = False
                    if pred_text in gt_text or gt_text in pred_text:
                        em_flag = True
                    else:
                        sim_score = sim_calc_by_bert([pred_text, gt_text])
                        if sim_score > 0.8:
                            em_flag = True
                    if em_flag:
                        results["em_search_ok"] = 1
                        results["em_ok"] = 1
            elif pred_type == "scroll":
                results["scroll_ok"] = 1
                results["em_ok"] = 1
            elif pred_type == "wait":
                results["wait_ok"] = 1
                results["em_ok"] = 1
            elif pred_type == "finish":
                results["finsh_ok"] = 1
                results["em_ok"] = 1

        return results

    def _update_metrics(self, results):
        """Update metrics with results from a single step"""
        self.total_step += results["total_step"]
        self.click_total_step += results["click_total_step"]
        self.search_total_step += results["search_total_step"]
        self.scroll_total_step += results["scroll_total_step"]
        self.wait_total_step += results["wait_total_step"]
        self.finish_total_step += results["finish_total_step"]
        self.pred_click += results["pred_click"]
        self.pred_search += results["pred_search"]
        self.pred_scroll += results["pred_scroll"]
        self.pred_wait += results["pred_wait"]
        self.pred_finsh += results["pred_finsh"]
        self.pred_error += results["pred_error"]
        self.pixel_dist += results["pixel_dist"]
        self.pixel_dist_cnt += results["pixel_dist_cnt"]
        self.tm_ok += results["tm_ok"]
        self.em_ok += results["em_ok"]
        self.tm_click_ok += results["tm_click_ok"]
        self.em_click_ok += results["em_click_ok"]
        self.tm_search_ok += results["tm_search_ok"]
        self.em_search_ok += results["em_search_ok"]
        self.scroll_ok += results["scroll_ok"]
        self.wait_ok += results["wait_ok"]
        self.finsh_ok += results["finsh_ok"]
        self.total_infer_time += results["infer_time"]
        self.total_steps += 1

    def infer(self, input_file, output_csv=None):
        if output_csv is None:
            output_csv = f"results_{int(time.time())}.xlsx"

        total_start_time = time.time()
        img_base_path = "work/mini/myeval/out/"

        # 准备CSV数据
        csv_data = []

        with open(input_file, "r", encoding="utf-8") as f:
            data = json.load(f)
            self.total_samples = len(data)
            # 添加外层进度条，用于显示样本处理进度
            for d in tqdm(data, desc="Processing samples", unit="sample"):
                idx = d["id"]
                appid = d["appId"]
                instruction = d["instruction"]
                ops = d["operations"]
                all_imgs = []
                all_actions = []

                for num, op in enumerate(ops):
                    screenWidth = op["screenWidth"]
                    screenHeight = op["screenHeight"]
                    url = op["url"]
                    type = op["type"]
                    output_path = f"{img_base_path}/{self.task_name}/{idx}/{num}.jpg"
                    all_imgs.append(output_path)
                    self._download_image_with_curl(url, output_path)
                    config = op["markConfig"]
                    action = {
                        "type": "",
                        "x": 0,
                        "y": 0,
                        "direction": "",
                        "length": 0,
                        "width": 0.0,
                        "height": 0.0,
                        "text": "",
                        "screenWidth": screenWidth,
                        "screenHeight": screenHeight,
                    }
                    action["type"] = type
                    if type in ["click", "input", "scroll", "search", "input"]:
                        action["width"] = config["width"]
                        action["height"] = config["height"]
                        action["x"] = config["x"] * screenWidth
                        action["y"] = config["y"] * screenHeight
                        action["rx"] = (config["x"] + config["width"]) * screenWidth
                        action["ry"] = (config["y"] + config["height"]) * screenHeight
                        # 计算出中心位置
                        action["cx"] = (action["x"] + action["rx"]) / 2
                        action["cy"] = (action["y"] + action["ry"]) / 2

                    if type == "search":
                        action["type"] = "input"
                        action["text"] = op["text"]
                    all_actions.append(action)

                step = len(all_imgs)
                # 收集当前样本的所有结果
                sample_results = [None] * (step - 1)
                # 使用线程池处理推理任务
                with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                    futures = {}
                    for i in range(1, step):
                        pre_imgs = all_imgs[: i + 1]
                        gt_action = all_actions[i]
                        future = executor.submit(
                            self._process_single_step, pre_imgs, instruction, gt_action
                        )
                        futures[future] = i - 1
                        # futures.append(future)

                    # 收集结果并更新指标
                    for future in as_completed(futures):
                        try:
                            results = future.result()
                            self._update_metrics(results)
                            sample_results[futures[future]] = results
                        except Exception as e:
                            print(f"Error processing step: {e}")

                # 构建CSV行数据
                row = {"id": idx, "instruction": instruction, "appid": appid}

                # 添加每个步骤的数据
                for i, result in enumerate(sample_results, 1):
                    row[f"img{i}"] = result["last_img"]
                    row[f"gt_action{i}"] = json.dumps(
                        result["gt_action"], ensure_ascii=False
                    )
                    row[f"pred_action{i}"] = json.dumps(
                        result["pred_action"], ensure_ascii=False
                    )
                    row[f"status{i}"] = result["status"]

                csv_data.append(row)

        # 写入CSV文件
        if csv_data:
            wb = Workbook()
            ws = wb.active
            # 设置列宽
            ws.column_dimensions["A"].width = 15  # ID列
            ws.column_dimensions["B"].width = 50  # 指令列
            ws.column_dimensions["C"].width = 15  # AppID列
            # 写入表头
            headers = ["id", "instruction", "appid"]
            max_steps = max(len(row) - 3 for row in csv_data) // 4  # 每个步骤4个字段
            for i in range(1, max_steps + 1):
                headers.extend(
                    [f"img{i}", f"gt_action{i}", f"pred_action{i}", f"status{i}"]
                )
                # 设置图片列宽
                img_col = 3 + (i - 1) * 4 + 1
                ws.column_dimensions[get_column_letter(img_col)].width = 30

            ws.append(headers)

            # 写入数据行
            for row_idx, row in enumerate(csv_data, 2):  # 从第2行开始
                ws.cell(row=row_idx, column=1, value=row["id"])
                ws.cell(row=row_idx, column=2, value=row["instruction"])
                ws.cell(row=row_idx, column=3, value=row["appid"])

                # 写入每个步骤的数据
                for i in range(1, max_steps + 1):
                    col_offset = (i - 1) * 4 + 4  # 从第4列开始

                    # 写入图片
                    img_key = f"img{i}"
                    if img_key in row and row[img_key]:
                        img_path = os.path.normpath(
                            os.path.abspath(row[img_key])
                        ).replace("\\", "/")
                        if os.path.exists(img_path):
                            try:
                                img = Image(img_path)
                                img.width = 200  # 设置图片宽度
                                img.height = 400  # 设置图片高度
                                ws.add_image(
                                    img, f"{get_column_letter(col_offset)}{row_idx}"
                                )
                                # ws.add_image(img, f'{chr(ord("D") + (i-1)*4)}{row_idx}')
                            except Exception as e:
                                print(f"无法插入图片: {row[img_key]}, 错误: {e}")

                    # 写入其他数据
                    for j, key in enumerate(
                        [f"gt_action{i}", f"pred_action{i}", f"status{i}"], 1
                    ):
                        if key in row:
                            ws.cell(row=row_idx, column=col_offset + j, value=row[key])

            # 保存Excel文件
            wb.save(output_csv)

        total_time = time.time() - total_start_time
        print(f"\nResults saved to: {output_csv}")
        print("\n=== Time Metrics ===")
        print(f"Total running time: {total_time:.2f}s")
        print(f"Total samples processed: {self.total_samples}")
        print(f"Total inference steps: {self.total_steps}")
        if self.total_steps > 0:
            print(
                f"Average inference time per step: {self.total_infer_time/self.total_steps:.2f}s"
            )
        if self.total_samples > 0:
            print(f"Average time per sample: {total_time/self.total_samples:.2f}s")


def eval_singel_step(
    task_name, vl_prompt, base_url, model_name, input_path, max_workers=4
):
    inferv2 = EvalInferV2(
        task_name=task_name,
        vl_prompt=vl_prompt,
        base_url=base_url,
        model_name=model_name,
        max_workers=max_workers,
    )
    inferv2.infer(input_path)
    inferv2.print_metrics()


if __name__ == "__main__":
    task_name = "test1"
    vl_prompt = """
        你是一个小程序操作助手，上述图像是按顺序操作任务指令，最近一张是当前图像。
        当前指令: 如果页面有“点击添加小程序下次尽快找到”的弹窗，一定要优先关闭弹窗,如果指令提到地址和当前地址不对一定要切换地址，如果当前页面能够搜索优先使用搜索功能来实现指令，{}，请问在当前图像里我要怎么操作？当前只能有一种输出选项：只能回复接下来的一个动作，不能回复多个动作，并给出在页面的精确像素坐标（x y），最终输出格式严格遵循<思考>xxx</思考><answer>xxx</answer><summary>xxx</summary>这个格式。answer动作只能在以下6 类集合选择：1. click x y(代表点击像素坐标 x,y)，2. input x y t（代表在x y 选中输入框并输入文本t） 3. finish（代表任务成功结束） 4. stop（代表任务无法进行 进程终止）5. scroll x（代表下滑x个像素，x为负数代表上滑|x|个像素）6. wait t（代表当前页面处于加载中，可以等待t秒）。 思考内容：首先通过对话上下文判断上一步的操作是否生效，如果上一步操作无效，需要反思并采取正确的动作，例如 1.  input x y t 后页面是否有显示 input 的内容t，没有的话则操作未生效，考虑网页不支持本步input 而应该采取click x y 操作使用点击小程序拉起的键盘输入 2. click x y 后操作未生效考虑点击位置无效，应该采取点击其他位置click x1 y1实现指令 3. assistant重复输入相同命令表示当前操作无效，需要采取其他方法实现指令 4. 需要详细检查 input 的内容 t 是否正确显示在页面中 5. 最近两张图像没有变化时需要反思动作是否正确
        """
    base_url = "http://drhttpsvr.polaris:8000/v1/llm-luban-cpsmixrs_xiaode526_ck8100_export-0526-16"
    model_name = "llm-luban-cpsmixrs_xiaode526_ck8100_export-0526-16"
    input_path = "/data/workspace/work/mini/myeval/eval_data/xj-mark1-filtered.json"
    eval_singel_step(task_name, vl_prompt, base_url, model_name, input_path, 24)
    # /data/workspace/work/mini/myeval/eval200.json
    # /data/workspace/work/mini/myeval/eval200_test.json
