## 1. 开发环境 Get Started
### 1.1 使用 Anaconda 安装 python 3.12

```
conda create -n py312 python=3.12
source activate py312
```

### 1.2 安装 VS Code 插件

```
# 必须安装的插件
"ms-python.python",
"ms-python.autopep8",
"ms-python.pylint",
# 可选择安装的插件
"mhutchie.git-graph",
"donjayamanne.githistory",
"eamodio.gitlens",
"github.copilot",
"yzane.markdown-pdf",
"ms-vscode-remote.remote-ssh"
```

### 1.3 设置 VS Code

```
# command + shift + p -> Preference: Open User Settings (Json)
{
    "python.analysis.extraPaths": ["./"],
    "python.analysis.typeCheckingMode": "basic",
    "python.linting.flake8Args": ["--config=.flake8"],
}
```

### 1.4 依赖管理 poetry

```
python -m pip install poetry==2.1.3
poetry install --no-root
```

### 1.5 pre-commit

```
# Install pre-commit
python -m pip install pre-commit
# 在这个整个仓库的根目录下（即 .git 所在的目录）运行下面这个命令
pre-commit install --config miniprogram/mmfinderdrsandboxagentsvr/deploy/pyfile/.pre-commit-config.yaml
```


## 2. 代码风格
### 规则

- 必须按照该文档安装 VS Code 的插件和 pre-commit
- VS Code 飘红提示必须解决，遇到 python 语言特性导致的飘红可以使用 `# type: ignore` 忽略
- 函数的出入参必须要有类型声明
- 写注释时，字母和汉字之间需要有一个空格

### 目录结构

- script：测试脚本
- sdk：wxms sdk
    - wxms：
        - middleware：各个中间件交互的模块定义
        - resources：资源文件，例如 js 文件
        - service：sdk 的业务代码
        - util：和业务无关的通用工具类
        - __init__.py：定义 sdk 为 python 的一个包，注意导包顺序，必须保证 logger 最先导包，否则会导致 opentelemetry 注册失效
        - env.py：环境变量注册
        - logger.py：日志类
        - model.py：定义各种类型
    - .gitignore：git 忽略提交的规则
    - build.sh：sdk 发版的脚本
    - poetry.lock：poetry lock 文件
    - pyproject.toml：project 依赖包的配置
- task_handler：各个任务类型的算法推理逻辑
- util：各个工具类
- .pre-commit-config.yaml：pre-commit 的配置
- main.py：主函数入口
- model.py：数据模型定义
- poetry.lock：poetry lock 文件
- pyproject.toml：project 依赖包的配置
- README.md：该文档


## 其他
### poetry 的注意事项

其他可能会用到的命令

```
# 增加依赖
poetry add {依赖名}
 
# 安装依赖
poetry install

# 更新依赖
poetry update {依赖名}

# 删除依赖
poetry remove {依赖名}

# 环境信息
poetry env info

# 展示依赖版本
poetry show -t
```

如果 poetry.lock 文件冲突，应该删除 `rm poetry.lock` 并使用 `poetry update` 重新生成 poetry.lock 文件

### sdk 开发和发版

```
# 开发 sdk 时为了解决 python 项目的路径问题，需要运行下面这个命令（**千万注意修改的代码是不是该项目中的代码**）
pip install -e ./sdk

# sdk 发版
cd ./sdk
vi pyproject.toml # 修改 sdk pyproject.toml 中的 version
./build.sh
cd ..
vi pyproject.toml # 修改 pyfile pyproject.toml sdk 的 version
rm poetry.lock
poetry update
# 推送代码即可自动触发镜像构建
```
